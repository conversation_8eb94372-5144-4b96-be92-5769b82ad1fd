// 更多
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        _pageUrl = "moreDetails/newMsg",
        _pageId = "#moreDetails_newMsg ";
    // var hasNewMsg_1 = 0,
    //     hasNewMsg_2 = 0,
    //     hasNewMsg_3 = 0,
    //     hasNewMsg_4 = 0;

    function init() {
        // hasNewMsg();
        //获取消息第一条
        getMessages()
        //获取咨询第一条
        getRealTimeInfo()
        //获取公告第一条
        getNotice()
        //获取未读消息数量
        getNoReadMsg()
    }
    function getMessages(){
        service.reqFun105003({}, function (data) {
            if (data.error_no == "0") {
                // console.log(data,'消息')
                if(data.results.length ){
                    let res = data.results[0]
                    $(_pageId + " .messageTitle").html(res.mail_title)
                    $(_pageId + " .messageTime").html(tools.ftime(res.create_date))
                }else{
                    $(_pageId + " .messageTitle").html('')
                    $(_pageId + " .messageTime").html('')
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }
    function getNoReadMsg(){
        service.reqFun105001({}, function (data) {
            if (data.error_no == "0") {
                let count = data.results[0].count*1
                if(count > 0){
                    $(_pageId + " .tips").html(count)
                    $(_pageId + " .tips").show()
                }else{
                    $(_pageId + " .tips").hide()
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }
    function getRealTimeInfo(){
        var param = {
            cur_page: '1',
            num_per_page: "1",
        };
        service.reqFun102050(param, function (data) {
            if (data.error_no == "0") {
                // let res = 
                if(data.results.length && data.results[0].data){
                    let res = data.results[0].data[0]
                    let time = res.createDate.substr(0,8)
                    $(_pageId + " .realTimeInfoTitle").html(res.title)
                    $(_pageId + " .realTimeInfoTime").html(tools.ftime(time))
                }else{
                    $(_pageId + " .realTimeInfoTitle").html('')
                    $(_pageId + " .realTimeInfoTime").html('')
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
        
    }
    function getNotice(){
        var param = {
            cur_page: '1',
            num_per_page: "1",
        };
        service.reqFun102055(param, function (data) {
            if (data.error_no == "0") {
                // console.log(data,'公告')
                if(data.results.length && data.results[0].data){
                    let res = data.results[0].data[0]
                    let time = res.createDate.substr(0,8)
                    $(_pageId + " .noticeTitle").html(res.title)
                    $(_pageId + " .noticeTime").html(tools.ftime(time))
                }else{
                    $(_pageId + " .noticeTitle").html('')
                    $(_pageId + " .noticeTime").html('')
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }
    function hasNewMsg() {

        // hasNewMsg_1 = appUtils.getLStorageInfo("hasNewMsg_1");//用户已看的公告id
        // hasNewMsg_2 = appUtils.getLStorageInfo("hasNewMsg_2");//最新公告id
        // hasNewMsg_3 = appUtils.getLStorageInfo("hasNewMsg_3");//用户已看的资讯id
        // hasNewMsg_4 = appUtils.getLStorageInfo("hasNewMsg_4");//最新资讯id

        // hasNewMsg_1 = hasNewMsg_1 ? hasNewMsg_1 : 0;
        // hasNewMsg_2 = hasNewMsg_2 ? hasNewMsg_2 : 0;
        // hasNewMsg_3 = hasNewMsg_3 ? hasNewMsg_3 : 0;
        // hasNewMsg_4 = hasNewMsg_4 ? hasNewMsg_4 : 0;

        // if (hasNewMsg_1 != hasNewMsg_2) {
        //     $("#moreDetails_msg #clicknotice span").addClass("on");
        // } else {
        //     $("#moreDetails_msg #clicknotice span").removeClass("on");
        // }
        // if (hasNewMsg_3 != hasNewMsg_4) {
        //     $("#moreDetails_msg #consultation span").addClass("on");
        // } else {
        //     $("#moreDetails_msg #consultation span").removeClass("on");
        // }
    }

    //绑定事件
    function bindPageEvent() {
        // 去 公告页面
        appUtils.bindEvent($(_pageId + " #clicknotice"), function () {
            //2017-8-17 jiaxr-add 去除新消息提示
            // $("#moreDetails_msg #clicknotice").removeClass("on");
            // appUtils.setLStorageInfo("hasNewMsg_1", hasNewMsg_2);
            // if (hasNewMsg_3 == hasNewMsg_4) {
            //     $(_pageId + ".hasNewMsg").removeClass("on");
            //     $("#account_myAccount #message_img").removeClass("on");
            //     appUtils.setLStorageInfo("hasNewMsg", "false");
            // }
            var param = {
                "prePage": "moreDetails/newMsg"
            };
            appUtils.pageInit("moreDetails/more", "moreDetails/notice", param);
        });

        // 去资讯页面
        appUtils.bindEvent($(_pageId + " #consultation"), function () {
            //2017-8-17 jiaxr-add 去除新消息提示
            // $("#moreDetails_msg #consultation span").removeClass("on");
            // appUtils.setLStorageInfo("hasNewMsg_3", hasNewMsg_4);
            // if (hasNewMsg_1 == hasNewMsg_2) {
            //     $(_pageId + ".hasNewMsg").removeClass("on");
            //     $("#account_myAccount #message_img").removeClass("on");
            //     appUtils.setLStorageInfo("hasNewMsg", "false");
            // }
            var param = {
                "prePage": "moreDetails/newMsg"
            };
            appUtils.pageInit("moreDetails/more", "moreDetails/consultation", param);
        });
        // 去消息页面
        appUtils.bindEvent($(_pageId + " #message"), function () {
            var param = {
                "prePage": "moreDetails/newMsg"
            };
            appUtils.pageInit("moreDetails/more", "moreDetails/message", param);
        });
        
        //返回
        appUtils.bindEvent($(_pageId + " #back_btn"), function () {
            pageBack();
        });


    }

    function destroy() {
        $(_pageId + " .tips").hide()
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var newMsg = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = newMsg;
});
