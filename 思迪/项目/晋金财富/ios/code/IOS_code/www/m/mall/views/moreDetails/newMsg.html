<div class="page" id="moreDetails_newMsg" data-pageTitle="消息" data-refresh="true">
<div class="pop_layer" style="display: none;" ></div>

	<section class="main fixed add_padding" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a href="javascript:void(0);" class="icon_back icon_gray" id="back_btn"><span>返回</span></a>
				<h1 class="text_gray text-center">消息</h1>
			</div>
		</header>
		<article class="no_padding">
			<!-- MORE_BOX START -->
			<!-- <div class="my_menu my_box">
				<p><a href="javascript:void(0);" id="clicknotice">资讯<span> </span></a></p>
				<p><a href="javascript:void(0);" id="consultation">公告<span> </span></a></p>
			</div> -->
			<div class="moreDetails_msg_list">
				<ul id="message" class="flex">
					<li class="pic flex flex_center">
						<img src="../images/news.jpg" alt="">
						<i style="display:none" class="tips">5</i>
					</li>
					<li class="right_remark">
						<p class="flex">
							<span class="m_font_size16 m_text_darkgray">消息提醒</span>
							<span class="m_font_size12 m_text_999 messageTime">--</span>
						</p>
						<p class="m_font_size12 m_text_999 messageTitle">--</p>
					</li>
				</ul>
				<ul id="clicknotice" class="flex">
					<li class="pic flex flex_center">
						<img src="../images/real_time_info.jpg" alt="">
						
					</li>
					<li class="right_remark">
						<p class="flex">
							<span class="m_font_size16 m_text_darkgray">资讯</span>
							<span class="m_font_size12 m_text_999 realTimeInfoTime">--</span>
						</p>
						<p class="m_font_size12 m_text_999 realTimeInfoTitle ellipsis">--</p>
					</li>
				</ul>
				<ul id="consultation" class="flex">
					<li class="pic flex flex_center">
						<img src="../images/notice.jpg" alt="">
					</li>
					<li class="right_remark">
						<p class="flex">
							<span class="m_font_size16 m_text_darkgray">公告</span>
							<span class="m_font_size12 m_text_999 noticeTime">--</span>
						</p>
						<p class="m_font_size12 m_text_999 noticeTitle ellipsis">--</p>
					</li>
				</ul>
			</div>
		</article>
	</section>
</div>