// 首页-登录成功后
//@版本: 2.0
define(function (require, exports, module) {
    var tools = require("../common/tools"); //是否有新消息
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        service = require("mobileService"),
        hIscroll = null,
        validatorUtil = require("validatorUtil"),
        _page_code = "login/userIndexs",
        _pageId = "#login_userIndexs ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var platform = gconfig.platform;
    var ut = require("../common/userUtil");
    var external = require("external");
    var timer;
    var dateObj = {
        "D": "天",
        "M": "月",
        "Y": "年",
    };
    var exclusive_buy_state = {
        // 购买状态 0开放购买前 1可购买 2结束购买之后，
        "0": {
            "btnClass": "",
            "btnText": "敬请期待"
        },
        "1": {
            "btnClass": "",
            "btnText": "购买"
        },
        "2": {
            "btnClass": "sold_out",
            "btnText": "售罄"
        },
    }

    function init() {
        var qualifiedInvestorStartAmount = require("gconfig").global.qualifiedInvestorStartAmount;
        $(_pageId + " .qualifiedInvestorStartAmount").html(qualifiedInvestorStartAmount / 10000 + "万");
        var channelCode = common.getLocalStorage("download_channel_code");
        if (channelCode == "yh") {
            //源晖用户跳转源晖首页
            appUtils.pageInit("", "yuanhui/userIndexs", {});
            return;
        } else if (channelCode == "yh_jjdx") {
            //源晖与财富共有用户,展示源晖入口
            $(_pageId + " #yuanhui").show();
            $(_pageId + " #inviteFriends").hide();
        } else if (!channelCode || channelCode == "jjdx") {
            //晋金财富用户,隐藏源晖入口
            $(_pageId + " #yuanhui").hide();
            $(_pageId + " #inviteFriends").show();
        } else {
            //其他渠道,跳转页面
            appUtils.pageInit("", "hengjipy/userIndexs", {});
            return;
        }
        appUtils.setSStorageInfo("routerList", ["login/userIndexs"]); //首页设置默认路径
        common.clearLocalStorage("activityInfo");
        common.clearLocalStorage("activityInfo_id");
        appUtils.clearSStorage("activityInfo");
        //首次进入页面，获取更新状态
        common.updateVers(_pageId);
        //自动更新，轮询查询
        if (!timer) {
            timer = setInterval(function () {
                common.updateVers(_pageId);
            }, global.updateTime);
        }
        // 苹果暗黑模式设置
        tools.getSystemMode(_pageId);

        //获取首页推荐产品
        //getJJAreaList();
        //获取产品列表

        getProductList();
        getExclusiveList();
        //延时加载，避免页面卡死
        setTimeout(() => {
            tools.guanggao({ _pageId: _pageId, group_id: "1" });
            if (!ut.getUserInf()) {
                $(_pageId + " #loginOut").text('登录');
                return;
            }
            $(_pageId + " #loginOut").text('退出');
            if (ut.getUserInf().bankAcct) { //查询绑卡状态
                //查询换卡状态，首页提示
                common.changeCardInter(function () {
                }, false);
            }
            if (!validatorUtil.isEmpty(ut.getUserInf().bankAcct) && (validatorUtil.isEmpty(ut.getUserInf().duty) || validatorUtil.isEmpty(ut.getUserInf().year_icome) || validatorUtil.isEmpty(ut.getUserInf().living_address_province)) && !appUtils.getSStorageInfo("hasShowUploadTip")) {
                appUtils.setSStorageInfo("hasShowUploadTip", true);
                layerUtils.iConfirm("根据监管要求，需要完善您的信息", function () {
                }, function () {
                    appUtils.pageInit(_page_code, "account/perfectInfo", {});
                }, "取消", "确定");
            }
            //活动弹框+定向弹窗
            AppProMsg();
            //首次登陆设置手势密码
            mobilePhoneControl();
            //身份证提示
            id_card_info()
        }, 0);

    }
    //去测评
    function pageTo_evaluation() {
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        }, '', '确定')
    }
    //身份证认证提示
    function id_card_info() {
        if (!appUtils.getSStorageInfo("user")) return
        //身份证认证状态 0:未完善 1:已完善 2:证件到期3:到期前3个月 4:到期后3个月
        let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
        let parentHtml = $(_pageId + ' .tip') //主节点
        let tip_mainText = $(_pageId + ' .tip' + ' .tip_mainText') //文案
        switch (perfect_info) {
            case '3':
                parentHtml.css("display", 'block')
                break;
            case '2':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                parentHtml.css("display", 'block')
                break;
            case '4':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                parentHtml.css("display", 'block')
                break;
            default:
                break;
        }
    }
    //活动弹窗
    function AppProMsg() {
        service.reqFun102018({}, function (data) {
            if (data.error_no == 0) {
                for (var i = 0; i < data.results.length; i++) {
                    var result = data.results[i];
                    var content = result.content; //展示内容
                    var type = result.type; //0 只弹一次 1.每天提示 2.定向弹窗  3.在某段时间内，登陆一次弹一次
                    var url_type = result.url_type; //1:內连  0：外链
                    var url = result.url; //url
                    var activityId = result.id; //ID
                    var activity = appUtils.getLStorageInfo("app_activity");
                    if (type == 2 && content) { //定向弹窗
                        var isShowLay = $(_pageId + " .special_pop_layer").css("display");
                        if (isShowLay != "block") {
                            $(_pageId + " #speciaDialog #src_Image img").attr("src", global.oss_url + content);
                            $(_pageId + " #speciaDialog").attr("urls", url);
                            $(_pageId + " #speciaDialog").attr("urlType", url_type);
                            //$(_pageId + " .special_pop_layer").show();
                            $(_pageId + " #speciaDialog").show();
                            $(_pageId + " #speciaDialog").attr("activityId", activityId);
                        }
                    } else if (type == 3) { //活动弹窗（每次进入页面都弹框）
                        if (!appUtils.getSStorageInfo("isShowAppProMsg")) {
                            appUtils.setSStorageInfo("isShowAppProMsg", true);
                            activityShow(_pageId, content, type, url_type, url, activityId, true);
                        }
                    } else {
                        if (activity) {
                            var aid = activity.activeId; //活动ID
                            //活动相同
                            if (aid == activityId) {
                                if (type == 1) {
                                    activityShow(_pageId, content, type, url_type, url, activityId, false);
                                }
                            } else {
                                activityShow(_pageId, content, type, url_type, url, activityId, true);
                            }
                        } else {
                            activityShow(_pageId, content, type, url_type, url, activityId, false);
                        }
                    }
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }

    //活动展示-
    function activityShow(_pageId, content, type, url_type, url, activityId, flag) {
        var date = new Date();
        var isNews = isNewDay(date.getFullYear(), date.getMonth() + 1, date.getDate());
        if (flag || isNews) {
            var html = '<div class="activityDialog pop_layer" style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;" urls="' + url + '" urltype="' + url_type + '" activityid="' + activityId + '" activetype="' + type + '"><div class="index-popup" style="width: 75%;z-index: 2100;position: static;"><img id="src_Image" class="popup-description" src= ' + global.oss_url + content + ' style="width:100%;height:100%;"><hr><a class="index-popup__btn-close"></a></div></div>'
            $(_pageId + " #activityDialog").append(html);

        }
    }

    //判断是否是新的一天
    /* @param oyear  当前年
     * @param omonth 当前月
     * @param oday   当前天
     * return true 新的一天
     * 不对手机时间自行修改到账时间混乱判断
     */
    function isNewDay(oyear, omonth, oday) {
        var activedate = appUtils.getLStorageInfo("app_activedate");
        if (activedate) {
            var ayear = activedate.year;
            var amonth = activedate.month;
            var aday = activedate.day;
            if (oyear == ayear && omonth == amonth && oday == aday) {
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    }

    function bindPageEvent() {
        //认证身份证
        appUtils.bindEvent($(_pageId + " .tip .uploadIDCard"), () => {
            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
        })
        //关闭更新
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + ' .update_prompt').hide();
        });
        //退出登录
        appUtils.bindEvent($(_pageId + " #loginOut"), function () {
            if (ut.getUserInf()) {
                layerUtils.iConfirm("确定退出吗？", function () {
                    service.reqFun1100004({}, function (data) {
                        if (data.error_no == "0") {
                            appUtils.clearSStorage(true);
                            $(_pageId + " .tip").hide();
                            /*$(_pageId + " .highEnd_area").hide();
                            $(_pageId + " .highEnd_list").hide();*/
                            $(_pageId + " #loginOut").text("登录");
                        } else {
                            layerUtils.iMsg(-1, data.error_info);
                        }
                    });
                    getProductList("2");
                }, function () {
                });
            } else {
                common.gestureLogin();
                // appUtils.pageInit(_page_code, "login/userLogin", {});
            }
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_page_code)
        });

        //bannear 点击链接
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            // if (!common.loginInter()) return;
            var file_type = $(this).attr("file_type"); // 链接类型 0 内链 1 外链 2 授权登录
            var url = $(this).attr("url");
            var file_state = $(this).attr("file_state"); //是否有效 0 无效 1 有效
            var name = $(this).attr("name");
            var description = $(this).attr("description");

            // 是否是有效内链
            if (file_type == "0" && file_state == "1" && url) {
                appUtils.pageInit("login/userIndexs", url, {});
                return;

            }
            // 是否是有效内外链接
            if (file_type == "1" && file_state == "1" && url) {
                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                    "url": url,
                    "name": name,
                    "description": description,
                });
                return;
            }
            // 登录授权
            if (file_type == "2" && file_state == "1" && url) {
                if (!common.loginInter()) return;
                if (!/^(http|https):/.test(url)) {
                    if (url.indexOf("?") > -1) {
                        var skip_url = url.split("?")[0];
                        var parameter = url.split("?")[1];
                        var parameter_arr = parameter.split("&"); //各个参数放到数组里
                        var urlInfo = {};//url的参数信息
                        for (var i = 0; i < parameter_arr.length; i++) {
                            num = parameter_arr[i].indexOf("=");
                            if (num > 0) {
                                name = parameter_arr[i].substring(0, num);
                                value = parameter_arr[i].substr(num + 1);
                                urlInfo[name] = value;
                            }
                        }
                        if (url.indexOf("activity/fundLuckdrawnew") > -1) {urlInfo['channel']="jjcf_app";urlInfo['type']="luckDraw"}
                        appUtils.pageInit("login/userIndexs", skip_url,urlInfo);
                    }else {
                        appUtils.pageInit("login/userIndexs", url);
                    }
                    return;
                }
                if (url.indexOf("activity/fundLuckdraw") > -1) {
                    common.setLocalStorage("activityInfo", {
                        activity_id: "2505",
                        cust_no: ut.getUserInf().custNo,
                        channel: "jjcf_app",
                        type: "luckDraw",
                        mobile: ut.getUserInf().mobileWhole,
                    })
                    let data = external.callMessage({
                        "funcNo": "50043",
                        "moduleName": "mall",
                        "key": "activityInfo",
                    })
                }
                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                    "url": url,
                    "name": name,
                    "description": description,
                });
            }
        }, 'click');


        //关闭特定活动弹窗
        appUtils.bindEvent($(_pageId + " #speciaDialog .index-popup__btn-close"), function () {
            //调用接口，使弹框值出现一次，messageType:
            var id = $(_pageId + " #speciaDialog").attr("activityId");
            service.reqFun101029({ "id": id }, function () {
                $(_pageId + " #updateLevel").hide();
                $(_pageId + " .updateLevel_layer").hide();
                var user = ut.getUserInf();
                user['messageInfo'] = "";
                ut.saveUserInf(user);
                $(_pageId + " #speciaDialog").hide();
                // $(_pageId + " .pop_layer").hide();
            });
        });

        //定向弹窗跳转
        appUtils.bindEvent($(_pageId + " #speciaDialog .popup-description"), function () {
            var url = $(this).parents("#speciaDialog").attr("urls");
            var file_type = $(this).parents("#speciaDialog").attr("urltype");
            //调用接口，使弹框值出现一次，messageType:1 晋金宝升级 2 定向弹窗
            var id = $(_pageId + " #speciaDialog").attr("activityId");
            service.reqFun101029({ "id": id }, function () {
                $(_pageId + " #updateLevel").hide();
                $(_pageId + " .updateLevel_layer").hide();
                $(_pageId + " #speciaDialog").hide();
                // $(_pageId + " .pop_layer").hide();
                // 是否是有效内连接
                if (file_type == "0" && url != "" && url != undefined && url != null) {
                    if (url.indexOf("activity/fundLuckdrawnew") > -1) {
                        if (url.indexOf("?") > -1) {
                            var skip_url = url.split("?")[0];
                            var parameter = url.split("?")[1];
                            var parameter_arr = parameter.split("&"); //各个参数放到数组里
                            var urlInfo = {};//url的参数信息
                            for (var i = 0; i < parameter_arr.length; i++) {
                                num = parameter_arr[i].indexOf("=");
                                if (num > 0) {
                                    name = parameter_arr[i].substring(0, num);
                                    value = parameter_arr[i].substr(num + 1);
                                    urlInfo[name] = value;
                                }
                            }
                            if (url.indexOf("activity/fundLuckdrawnew") > -1) {
                                urlInfo['channel'] = "jjcf_app";
                                urlInfo['type']="luckDraw";
                            }
                            appUtils.pageInit("login/userIndexs", skip_url, urlInfo);
                        } else {
                            appUtils.pageInit("login/userIndexs", url);
                        }
                        return;
                    }
                    appUtils.pageInit(_page_code, url, {});
                }
                // 是否是有效外链
                if (file_type == "1" && url != "" && url != undefined && url != null) {
                    if (url.indexOf("activity/fundLuckdraw") > -1) {
                        common.setLocalStorage("activityInfo", {
                            activity_id: "2505",
                            cust_no: ut.getUserInf().custNo,
                            channel: "jjcf_app",
                            type: "luckDraw",
                            mobile: ut.getUserInf().mobileWhole,
                        })
                        let data = external.callMessage({
                            "funcNo": "50043",
                            "moduleName": "mall",
                            "key": "activityInfo",
                        })
                    }
                    appUtils.pageInit(_page_code, "guide/advertisement", {
                        "url": url,
                        "prePage_code": _page_code
                    });
                }

            });
        });

        //关闭活动弹窗
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog .index-popup__btn-close", function () {
            var date = new Date();
            if ($(this).parents(".activityDialog").attr("activetype") !== "3") {
                var activityId = $(this).parents(".activityDialog").attr("activityId");
                var activetype = $(this).parents(".activityDialog").attr("activetype");

                appUtils.setLStorageInfo("app_activity", {
                    "activeId": activityId,
                    "activetype": activetype
                });
                appUtils.setLStorageInfo("app_activedate", {
                    "year": date.getFullYear(),
                    "month": date.getMonth() + 1,
                    "day": date.getDate()
                });
            }
            $(this).parents(".activityDialog").remove();
        }, 'click');

        //活动弹窗跳转
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog .popup-description", function () {
            var date = new Date();
            if ($(this).parents(".activityDialog").attr("activetype") !== "3") {
                var activityId = $(this).parents(".activityDialog").attr("activityId");
                var activetype = $(this).parents(".activityDialog").attr("activetype");
                appUtils.setLStorageInfo("app_activity", {
                    "activeId": activityId,
                    "activetype": activetype
                });
                appUtils.setLStorageInfo("app_activedate", {
                    "year": date.getFullYear(),
                    "month": date.getMonth() + 1,
                    "day": date.getDate()
                });
            }
            var url = $(this).parents(".activityDialog").attr("urls");
            var file_type = $(this).parents(".activityDialog").attr("urltype");
            $(this).parents(".activityDialog").remove();
            // 是否是有效内连接
            if (file_type == "0" && url) {
                appUtils.pageInit(_page_code, url, {});
                if (url.indexOf("activity/fundLuckdrawnew") > -1) {
                    if (url.indexOf("?") > -1) {
                        var skip_url = url.split("?")[0];
                        var parameter = url.split("?")[1];
                        var parameter_arr = parameter.split("&"); //各个参数放到数组里
                        var urlInfo = {};//url的参数信息
                        for (var i = 0; i < parameter_arr.length; i++) {
                            num = parameter_arr[i].indexOf("=");
                            if (num > 0) {
                                name = parameter_arr[i].substring(0, num);
                                value = parameter_arr[i].substr(num + 1);
                                urlInfo[name] = value;
                            }
                        }
                        if (url.indexOf("activity/fundLuckdrawnew") > -1) {
                            urlInfo['channel'] = "jjcf_app";
                            urlInfo['type']="luckDraw";
                        }
                        appUtils.pageInit("login/userIndexs", skip_url, urlInfo);
                    } else {
                        appUtils.pageInit("login/userIndexs", url);
                    }
                    return;
                }
            }
            // 是否是有效外链
            if (file_type == "1" && url) {
                if (url.indexOf("activity/fundLuckdraw") > -1) {
                    common.setLocalStorage("activityInfo", {
                        activity_id: "2505",
                        cust_no: ut.getUserInf().custNo,
                        channel: "jjcf_app",
                        type: "luckDraw",
                        mobile: ut.getUserInf().mobileWhole,
                    })
                    let data = external.callMessage({
                        "funcNo": "50043",
                        "moduleName": "mall",
                        "key": "activityInfo",
                    })
                }
                appUtils.pageInit(_page_code, "guide/advertisement", {
                    "url": url,
                    "prePage_code": _page_code
                });
            }

        }, 'click');

        //充值
        appUtils.bindEvent($(_pageId + " #recharge"), function () {
            tools.clickPoint(_pageId, _page_code, 'recharge');
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            //到期3个月后提示
            if (perfect_info == 4) {
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换");
            }
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消");
                    return;
                } else if (invalidFlag == '1') {
                    pageTo_evaluation()
                    return
                }
                appUtils.pageInit(_page_code, "thfund/inputRechargePwd", {});
            });
        });

        //取现
        appUtils.bindEvent($(_pageId + " #enchashment"), function () {
            tools.clickPoint(_pageId, _page_code, 'enchashment')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            if (perfect_info == 4) {
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换");
            }
            //查询是否已开银行账户
            service.reqFun151110({ bank_channel_code: "" }, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var results = data.results[0];
                if (results.bank_flag == "0") { //未开银行账户
                    common.changeCardInter(function () {
                        appUtils.pageInit(_page_code, "thfund/enchashment");
                    })
                    return;
                }
                service.reqFun151107({}, function (data) { //查询银行资产
                    if (data.error_no != "0") {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var results = data.results[0];
                    if (results && results.total_yue > 0) { //银行余额大于0，进入取现主页
                        layerUtils.iLoading(false);
                        appUtils.pageInit(_page_code, "thfund/enchashmentHome");
                    } else {
                        common.changeCardInter(function () {
                            appUtils.pageInit(_page_code, "thfund/enchashment");
                        })
                    }
                }, { isLastReq: false })
            }, { isLastReq: false })

        });

        // //晋金宝
        // appUtils.bindEvent($(_pageId + " #thfund"), function () {
        //     tools.clickPoint(_pageId, _page_code, 'thfund')
        //     if (!common.loginInter()) return;
        //     if (!ut.hasBindCard(_page_code)) return;
        //     common.changeCardInter(function () {
        //         appUtils.pageInit(_page_code, "thfund/myProfit", {});
        //     })
        // });
        //机构理财
        appUtils.bindEvent($(_pageId + " #institFinancial"), function () {
            tools.clickPoint(_pageId, _page_code, 'institFinancial')
            if (!common.loginInter()) return;
            // if (!ut.hasBindCard(_page_code)) return;
            common.changeCardInter(function () {
                appUtils.pageInit(_page_code, "institutions/institFinancial", {});
            })
        });

        //我的资产
        appUtils.bindEvent($(_pageId + " #myAsset"), function () {
            tools.clickPoint(_pageId, _page_code, 'myAsset')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            common.changeCardInter(function () {
                appUtils.pageInit(_page_code, "account/myAssets", {});
            })
        });

        //银行存款
        appUtils.bindEvent($(_pageId + " #bankDeposit"), function () {
            tools.clickPoint(_pageId, _page_code, 'bankDeposit')
            appUtils.pageInit(_page_code, "bank/bankList", {});
        });
        //邀请好友
        appUtils.bindEvent($(_pageId + " #inviteFriends"), function () {
            tools.clickPoint(_pageId, _page_code, 'inviteFriends')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            appUtils.pageInit(_page_code, "vipBenefits/friendInvitation", {});
        });

        //定期理财
        appUtils.bindEvent($(_pageId + " #inclusive"), function () {
            tools.clickPoint(_pageId, _page_code, 'inclusive')
            appUtils.setSStorageInfo("recommend_type", "102");
            appUtils.pageInit(_page_code, "inclusive/fundList", {});
        });
        //活期理财
        appUtils.bindEvent($(_pageId + " #liveFundList"), function () {
            tools.clickPoint(_pageId, _page_code, 'liveFundList')
            appUtils.setSStorageInfo("recommend_type", "002");
            appUtils.pageInit(_page_code, "inclusive/liveFundList", {});
        });
        //晋金高端
        appUtils.bindEvent($(_pageId + " #highEnd"), function (e) {
            e.preventDefault();
            tools.clickPoint(_pageId, _page_code, 'highEnd')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            appUtils.pageInit(_page_code, "highEnd/fundList", {});
        });

        //基金超市
        appUtils.bindEvent($(_pageId + " #fundMarket"), function () {
            tools.clickPoint(_pageId, _page_code, 'fundMarket')
            appUtils.pageInit(_page_code, "fundMarket/fundList", {});

        });
        //源晖专享
        appUtils.bindEvent($(_pageId + " #yuanhui"), function () {
            tools.clickPoint(_pageId, _page_code, 'yuanhui')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            appUtils.pageInit(_page_code, "yuanhui/fundList", {});
        });


        //购买银行存款产品
        appUtils.preBindEvent($(_pageId + " .bank_list"), ".item", function () {
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            appUtils.setSStorageInfo("productInfo", productInfo);
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            if (productInfo.prod_status != "2") {
                layerUtils.iAlert("产品已售罄");
                return;
            }
            service.reqFun151110({ bank_channel_code: productInfo.bank_channel_code }, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.results[0].bank_flag == 0) { //未开户
                    appUtils.pageInit(_page_code, "bank/faceRecognition");
                    return;
                }
                appUtils.pageInit(_page_code, "bank/purchase");
            })
        }, 'click');

        // 银行存款列表页
        appUtils.bindEvent($(_pageId + " .bank_area .more"), function () {
            appUtils.pageInit(_page_code, "bank/bankList");
        }, 'click');

        // 晋金普惠列表页  更多入口
        appUtils.bindEvent($(_pageId + " .inclusive_area .more"), function () {
            appUtils.setSStorageInfo("recommend_type", "102");
            appUtils.pageInit(_page_code, "inclusive/fundList");
        }, 'click');
        //活期理财  更多入口
        appUtils.bindEvent($(_pageId + " .livefund_area .more"), function () {
            appUtils.setSStorageInfo("recommend_type", "002");
            appUtils.pageInit(_page_code, "inclusive/liveFundList", {});
        });
        // 晋金高端列表页 更多入口
        appUtils.bindEvent($(_pageId + " .highEnd_area .more"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            appUtils.pageInit(_page_code, "highEnd/fundList");
        }, 'click');

        //点击我的
        appUtils.bindEvent($(_pageId + " #wode"), function () {
            if (!common.loginInter()) return;
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            if (ut.getUserInf().bankAcct) {
                appUtils.pageInit(_page_code, "account/myAccount", {});
            } else {
                appUtils.pageInit(_page_code, "account/myAccountNoBind", {});
            }
        });

        // 点击跳转到 更多页面
        appUtils.bindEvent($(_pageId + " #gengduo"), function () {
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_page_code, "moreDetails/more", {});
        });
        //活期理财
        appUtils.preBindEvent($(_pageId + " .liveFund_list"), ".item", function () {
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            let invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null
            if (productInfo.prod_sub_type == "10") { //晋金宝判断登录状态
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消");
                        return;
                    } else if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) {
                        pageTo_evaluation()
                        return
                    }
                    //到期3个月后提示
                    if (perfect_info == 4) {
                        return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                        }, "取消", "更换");
                    }
                    appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
                    appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
                    appUtils.pageInit(_page_code, "thfund/inputRechargePwd", {});
                });
                return;
            }
            // if((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) return pageTo_evaluation()
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);

            tools.jumpDetailPage(_page_code, productInfo.prod_sub_type, productInfo.prod_sub_type2)


        }, 'click');
        // 定期理财列表进详情页
        appUtils.preBindEvent($(_pageId + " .inclusive_list"), ".item", function (e) {
            // let invalidFlag = appUtils.getSStorageInfo("user")?appUtils.getSStorageInfo("user").invalidFlag:null
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            // if((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) return pageTo_evaluation()
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
            tools.jumpDetailPage(_page_code, productInfo.prod_sub_type, productInfo.prod_sub_type2)
        });
        // 晋金高端进详情页
        appUtils.preBindEvent($(_pageId + " .highEnd_list"), ".item", function (e) {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            if ($(this).hasClass("unactive")) {
                appUtils.pageInit(_page_code, "highEnd/fundList");
                return;
            }
            // let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            // if((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) return pageTo_evaluation()
            var userState = JSON.parse($(this).find(".btn").attr("userState"));
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("productInfo", productInfo);
            if (userState.state != "1") { //未做过合格投资人认证
                $(_pageId + ".qualifiedInvestor").show();
                return;
            }
           
            //TODO： 如果有产品营销页模板 - 跳转营销页 没有则跳详情
            productInfo.prod_propagate_temp = "1";
            if (productInfo.prod_propagate_temp) {
                appUtils.pageInit(_page_code, "template/marketing");
            } else {
                tools.jumpPriDetailPage(_page_code, productInfo.prod_sub_type2);
            }



        });
        //专享入口
        appUtils.preBindEvent($(_pageId + " .exclusive_list"), ".item", function () {
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            let invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null
            productInfo.fund_code = productInfo.prod_id;//产品编码
            if (productInfo.exclusive_product_type == "02") { //公募 大集合
                appUtils.setSStorageInfo("fund_code", productInfo.prod_id);
                appUtils.setSStorageInfo("productInfo", productInfo);
                appUtils.pageInit(_page_code, "thfund/gatherDetail");
                return;
            }
            if (productInfo.exclusive_product_type == "04") { //公募 持有期
                appUtils.setSStorageInfo("fund_code", productInfo.prod_id);
                appUtils.setSStorageInfo("productInfo", productInfo);
                appUtils.pageInit(_page_code, "inclusive/holdsDetail");
                return;
            }
            //银行 新手专享   10点抢购
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) {
                pageTo_evaluation()
                return
            }
            if (productInfo.buy_state == "0" && productInfo.exclusive_product_type != "03") { // 不包括货基
                layerUtils.iAlert("敬请期待");
                return;
            }
            if (productInfo.buy_state == "2" && productInfo.exclusive_product_type != "03") { // 不包括货币基金
                layerUtils.iAlert("产品已售罄");
                return;
            }

            if (productInfo.exclusive_product_type == "03") { //货币基金
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消");
                        return;
                    }
                    appUtils.setSStorageInfo("fund_code", productInfo.prod_id);
                    productInfo["prod_sub_type"] = "10";
                    appUtils.setSStorageInfo("productInfo", productInfo);
                    appUtils.pageInit(_page_code, "inclusive/moneytaryPurchase");
                });
                return;
            }

            productInfo.prod_code = productInfo.prod_id;//产品编码
            productInfo.surv_amt = productInfo.prod_per_min_amt; //起购金额
            productInfo.bas_int_rate = productInfo.rate; //存款利率
            appUtils.setSStorageInfo("productInfo", productInfo);

            service.reqFun151110({ bank_channel_code: productInfo.bank_channel_code }, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.results[0].bank_flag == 0) { //未开户
                    layerUtils.iLoading(false);
                    appUtils.pageInit(_page_code, "bank/faceRecognition");
                    return;
                }
                if (productInfo.exclusive_type == "2") { //新手专享
                    service.reqFun109003({}, function (data) { //查询是否新手
                        if (data.error_no != "0") {
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                        var is_sales_tiro = data.results[0].is_sales_tiro; // 是否新手（0-是 1-否）
                        if (is_sales_tiro != "0") {
                            layerUtils.iAlert("您已不是新手");
                            return;
                        }
                        appUtils.pageInit(_page_code, "bank/purchase");
                    })
                    return;
                }
                appUtils.pageInit(_page_code, "bank/purchase");
            }, { isLastReq: false })
        }, 'click');

        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    $(_pageId + ".qualifiedInvestor").hide();
                    getProductList("1");
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });
        // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            appUtils.clearSStorage("fund_code");
            appUtils.clearSStorage("productInfo");
            $(_pageId + ".qualifiedInvestor").hide();
        });
    }

    //获取产品列表
    function getProductList(type) {
        //获取产品列表
        service.reqFun102020({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                //获取活期理财列表
                results.hqList.length > 0 && getHQList(results.hqList);
                //获取银行存款列表
                // getBankList(results.yhList);
                //获取定期理财列表
                results.dqList.length > 0 && getInclusiveList(results.dqList);
                //获取高端列表
                $(_pageId + " .highEnd_area").show();
                if (results.gdList.length > 0) {
                    $(_pageId + ' .highEnd_area .more').show();
                    results.gdList.length > 0 && getHighEndList(results.gdList, type);
                } else {
                    $(_pageId + ' .highEnd_area .more').hide();
                    $(_pageId + ' .highEnd_list').html('<div class="item unactive">\n' +
                        '   <div class="title" style="height: 0.6rem;line-height:0.6rem;font-size:0.14rem;">此栏为高端理财产品，仅面向合格投资者</div>\n' +
                        '   <a href="javascript:void(0)" class="buy_btn pop in">购买</a>\n' +
                        '</div>')
                }
            }
        })

    }

    function getExclusiveList() {
        //获取专享产品列表
        service.reqFun109002({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    $(_pageId + " .exclusive").hide();
                    return;
                }
                var html = "";
                for (var i = 0; i < results.length; i++) {
                    var exclusive_product_type = results[i].exclusive_product_type; //专享产品类型  01 银行  02 大集合  03 货基
                    var exclusive_type = results[i].exclusive_type;//专享类型 8 住房专享
                    if (exclusive_product_type == "01") { //银行
                        // html += renderExclusiveBank(results[i]);
                    } else if (exclusive_product_type == "02") { //大集合
                        html += renderExclusiveBond(results[i]);
                    } else if (exclusive_product_type == "03") { //货基
                        if (exclusive_type == "8") {
                            html += renderExclusiveHousing(results[i]);
                        } else {
                            html += renderExclusiveMoney(results[i]);
                        }
                    } else if (exclusive_product_type == "04") { //持有期
                        html += renderExclusiveHold(results[i]);
                    }
                }
                $(_pageId + " .exclusive .exclusive_list").html(html);
                $(_pageId + " .exclusive ").show();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取活期理财列表
    function getHQList(data) {
        appUtils.setSStorageInfo("fund_code", data[0].fund_code);
        var html = "";
        for (var i = 0; i < data.length; i++) {
            //空数据处理
            html += showDetail(data[i]);
        }
        if (data.length == 0) {
            $(_pageId + " .livefund_area").hide();
        } else {
            $(_pageId + " .livefund_area").show();
        }
        $(_pageId + " .liveFund_list .item_box").html(html);
    }

    //获取银行存款列表
    function getBankList(productList) {
        if (!productList) return;
        var str = "", btnClass = "", btnText = "", depDayTypeName;

        for (var i = 0; i < productList.length; i++) {
            var prod_code = productList[i].prod_code; //产品代码
            var prod_name = productList[i].prod_name; //产品名称
            var surv_amt = productList[i].surv_amt; //起存金额（元）
            var pay_int_type = productList[i].pay_int_type;  //付息单位 D-天 M-月 Y-年
            var pay_int_hz = productList[i].pay_int_hz; //付息周期（天数）
            var dep_day_type = productList[i].dep_day_type;  //周期单位 D-天 M-月 Y-年
            var prod_dep_day = productList[i].prod_dep_day; //周期（天数）
            var bas_int_rate = productList[i].bas_int_rate; //基础利率
            var is_transfer = productList[i].is_transfer;//可转让
            var prod_status = productList[i].prod_status; //产品状态 2-发售 4-售罄 6-停售
            var brnd_sris = productList[i].brnd_sris; //产品系列   SD001 众力存  SD002 中惠存
            if (brnd_sris == "SD002") { //产品不存在周期付息，展示存期
                depDayTypeName = dateObj[pay_int_type];
                var dateStr = "<span>每" + pay_int_hz + depDayTypeName + "付息</span>";
            } else if (brnd_sris == "SD001") {
                depDayTypeName = dateObj[dep_day_type];
                pay_int_hz = prod_dep_day;
                var dateStr = "<span>期限：" + pay_int_hz + depDayTypeName + "</span>";
            }
            if (prod_status == "2") {
                btnClass = "";
                btnText = "购买";
            } else if (prod_status == "4") {
                btnClass = "sold_out";
                btnText = "售罄";
            } else if (prod_status == "6") {
                btnClass = "sold_out";
                btnText = "停售";
            }
            var transflagStr = "";
            if (is_transfer == "1") {
                transflagStr = '<span class="transflag">可转让</span>';
            }
            str += "<div class='item' prod_code='" + prod_code + "'><div class='box'>" +
                "<span style='display: none' class='productInfo'>" + JSON.stringify(productList[i]) + "</span>" +
                "<p class='title'>" + prod_name + transflagStr + "</p>" +
                "<div class='balance'><span>存款利率：<span class='num'>" + tools.fmoney(bas_int_rate) + "</span>%</span></div>" +
                "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + tools.fmoney(surv_amt) + "元</span>" + dateStr + "</p>" +
                "<em><i class='turn in'></i></em></p><a  href='javascript:void(0)' class='buy_btn pop btn" + btnClass + " in'>" + btnText + "</a></div></div>";
            $(_pageId + " .bank_list  .item_box").html(str);

        }
        if (productList.length == 0) {
            $(_pageId + " .bank_area").hide();
        } else {
            $(_pageId + " .bank_area").show();
        }
    }

    //获取晋金普惠列表
    function getInclusiveList(data) {
        var html = "";
        for (var i = 0; i < data.length; i++) {
            //空数据处理
            // data[i] = tools.FormatNull(data[i]);
            var annu_yield = data[i].annu_yield;
            if (annu_yield != "--") {
                annu_yield = (+annu_yield).toFixed(2) + "%";
            }
            html += showDetail(data[i]);
        }
        if (data.length == 0) {
            $(_pageId + " .inclusive_area").hide();
        } else {
            $(_pageId + " .inclusive_area").show();
        }
        $(_pageId + " .inclusive_list .item_box").html(html);
    }

    //获取高端推荐列表
    // type 1 确认做过合格投资着认证 2 未登录状态
    function getHighEndList(data, type) {
        var html = "";
        if (!data || data.length == 0) {
            $(_pageId + " .highEnd_area").hide();
            return;
        } else {
            $(_pageId + " .highEnd_area").show();
        }

        var fullData = function (userState) {
            for (var i = 0; i < data.length; i++) {
                var transferable = data[i].transferable;//是否可转让
                var str = "";
                if (transferable == "1") {
                    str = "<img src='" + global.oss_url + data[i].url + "' style='width: 14%;margin-left: 0.12rem;margin-top: -0.04rem;'>"
                }
                var recommend_info = data[i].recommend_info;
                var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";


                if (data[i].prod_sub_type2 == '100') {
                    /**
                     * 产品整合相关
                     */
                    let prod_name_list = data[i].prod_name_list //列表名称
                    let found_rate = data[i].found_rate ? data[i].found_rate : '--' //成立以来收益
                    let prod_sname = prod_name_list ? prod_name_list : data[i].prod_sname ? data[i].prod_sname : '--' //产品名称
                    let preincomerate = tools.fmoney(data[i].preincomerate) //年化标准
                    let threshold_amount = data[i].threshold_amount / 10000 //起购金额
                    let inrest_term = data[i].inrest_term   //封闭期/锁定期
                    let nav = tools.fmoney(data[i].nav, 4)
                    let this_year_rate = data[i].this_year_rate ? data[i].this_year_rate : '--'
                    // let recommend_info = data[i].recommend_info //提示

                    //产品整合 是否展示
                    let compare_benchmark_list = data[i].compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                    let fund_rate_list = data[i].fund_rate_list == '1' ? '' : 'display_none' //是否展示成立以来收益
                    let closed_period_list = data[i].closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                    let lock_period_list = data[i].lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                    let this_year_rate_list = data[i].this_year_rate_list == '1' ? '' : 'display_none' //是否展示今年以来收益
                    let nav_list = data[i].nav_list == '1' ? '' : 'display_none' //是否展示净值
                    let recommend_info_list = data[i].recommend_info_list == '1' ? '' : 'display_none'
                    let threshold_amount_list = data[i].threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                    html += "<div class='template_item'>" +
                        "<div class='item template_box flex'>" +
                        "<div style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</div>" +
                        "<ul class='template_left vertical_line m_list_color balance'>" +
                        "<li class='title m_font_size16 m_bold'>" + prod_sname + str + "</li>" +
                        "<li class='flex'>" +
                        "<p class='m_font_size12 " + compare_benchmark_list + "'><span>业绩计提基准(年化)：</span><span class='m_text_red m_font_size18'>" + preincomerate + "</span>%</p>" +
                        "<p class='m_font_size12 " + fund_rate_list + "'>成立以来收益：<span class='m_text_red m_font_size18'>" + (found_rate ? tools.fmoney(found_rate) : "--") + "</span>%</p>" +
                        "</li>" +
                        "<li class='m_font_size12 m_width_20rem flex wrap'>" +
                        "<p class='" + threshold_amount_list + "'>起购：<span>" + threshold_amount + "万元</span></p>" +
                        "<p class='" + closed_period_list + "'>期限：<span>" + inrest_term + "</span></p>" +
                        "<p class='" + lock_period_list + "'>锁定期：<span>" + inrest_term + "</span></p>" +
                        "<p class='" + this_year_rate_list + "'>今年来收益：<span>" + (this_year_rate ? tools.fmoney(this_year_rate) : "--") + "</span>%</p>" +
                        "<p class='" + nav_list + "'>最新净值：<span>" + nav + "</span></p>" +
                        "</li>" +
                        "<li class='m_font_size12 " + recommend_info_list + "'>" + recommend_info + "</li>" +
                        "</ul>" +
                        "<ul class='template_right level_center vertical_center'>" +
                        "<li  class='btn vertical_center flex level_center " + tools.priBtnObj(data[i].buy_state, data[i].prod_sub_type2).btnClass + " in' userState='" + JSON.stringify(userState) + "'>" + tools.priBtnObj(data[i].buy_state, data[i].prod_sub_type2).btnText + "</li>" +
                        "</ul>" +
                        "</div>"
                    "</div>";
                    // html += "<div class='item'><div class='box'>" +
                    //         "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                    //         "<p class='title'><span>" + data[i].prod_sname + str +"</span></p>" +
                    //         "<div class='balance'><span>业绩计提基准(年化)：<span class='num " + tools.addMinusClass(data[i].preincomerate) + "'>" + tools.fmoney(data[i].preincomerate) + "</span>%</span><span>成立以来收益："+ found_rate + "</span></div>" +
                    //         "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + data[i].threshold_amount / 10000 + "万元</span><span>期限：" + data[i].inrest_term + "</span></p>" +
                    //         recommend_info_str;
                } else if (data[i].prod_sub_type2 == "95") { //政金债
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + data[i].prod_sname + str + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num " + tools.addMinusClass(data[i].preincomerate) + "'>" + tools.fmoney(data[i].preincomerate) + "</span>%</span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + data[i].threshold_amount / 10000 + "万元</span><span>期限：" + data[i].inrest_term + "</span></p>" +
                        recommend_info_str;
                } else if (data[i].prod_sub_type2 == "94") { // 持有期
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + data[i].prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num text_red'>" + tools.fmoney(data[i].interest_rate_min) + '-' + tools.fmoney(data[i].interest_rate_max) + "</span>%</span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + data[i].threshold_amount / 10000 + "万元</span><span>锁定期：" + data[i].inrest_term + "</span></p>" +
                        recommend_info_str;
                } else {
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + data[i].prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num " + tools.addMinusClass(data[i].preincomerate) + "'>" + tools.fmoney(data[i].preincomerate) + "</span>%</span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + data[i].threshold_amount / 10000 + "万元</span><span>封闭期：" + data[i].inrest_term + "</span></p>" +
                        recommend_info_str;
                }
                if (data[i].prod_sub_type2 != '100') html += "</p><div  class='btn " + tools.priBtnObj(data[i].buy_state, data[i].prod_sub_type2).btnClass + " in' userState='" + JSON.stringify(userState) + "'>" + tools.priBtnObj(data[i].buy_state, data[i].prod_sub_type2).btnText + "</div></div></div>";
            }
        }


        var unknowData = function (btnText, userState) {
            for (var i = 0; i < data.length; i++) {
                let prod_name_list = data[i].prod_name_list //列表名称
                let prod_sname = prod_name_list ? prod_name_list : data[i].prod_sname ? data[i].prod_sname : '--' //产品名称
                var recommend_info = data[i].recommend_info;
                var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
                if (data[i].prod_sub_type2 == "95") { //政金债
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num'>--</span></span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：--</span><span>期限：--</span></p>" +
                        recommend_info_str;
                } else if (data[i].prod_sub_type2 == "94") { //持有期
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num'>--</span></span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：--</span><span>锁定期：--</span></p>" +
                        recommend_info_str;
                } else {
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num'>--</span></span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：--</span><span>封闭期：--</span></p>" +
                        recommend_info_str;

                }
                html += "</p><div  class='btn  in' userState='" + JSON.stringify(userState) + "'>" + btnText + "</div></div></div>";
            }
        }

        if (type == "1") {
            fullData({ state: "1" });
            $(_pageId + " .highEnd_list").html(html);
            return;
        }

        if (!ut.getUserInf() || type == "2") {
            unknowData("登录可见");
            $(_pageId + " .highEnd_list").html(html);
            return;
        }
        service.reqFun101057({}, function (resultVo) {
            if (resultVo.error_no != "0") {
                layerUtils.iAlert(resultVo.error_info);
                return;
            }
            var sm_white_state = resultVo.results[0].sm_white_state; //101057 sm_white_state 1是白名单 2不是白名单
            service.reqFun101037({}, function (datas) {
                if (datas.error_no != "0") {
                    layerUtils.iAlert(datas.error_info);
                    return;
                }
                var state = datas.results[0].state; //1 已确认
                var userState = {
                    "state": state,
                    "sm_white_state": sm_white_state,
                }
                if (sm_white_state == "1" || state == "1") { //白名单用户 || 合格投资人 展示详细数据
                    fullData(userState);
                } else {
                    unknowData("认证可见", userState);
                }
                $(_pageId + " .highEnd_list").html(html);

            });


        })
    }

    function showDetail(data) {
        var cssStyle;
        if (data.prod_sub_type == "50" || data.prod_sub_type == "10") {
            cssStyle = 'style="padding:0.15rem 0.1rem"';
        } else {
            cssStyle = "";
        }
        var pordStr;

        var recommend_info = data.recommend_info;
        var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
        if (data.prod_sub_type == "30") {
            pordStr = '<p class="title">晋金宝' + require("gconfig").global.holding_days + '天</p>';
        } else if (data.prod_sub_type == "10") {
            pordStr = '<p class="title">晋金宝</p>';
        } else {
            pordStr = '<p class="title">' + data.prod_sname + '</p>';
        }
        var html = '<div class="item" ' + cssStyle + '>' +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(data) + "</span>" +
            pordStr;
        if (data.prod_sub_type == "10") {
            var annu_yield = data.annu_yield;
            if (annu_yield != "--") {
                annu_yield = (+annu_yield).toFixed(2);
            }
            html += '<div class="balance">' +
                '<span>七日年化：</span>' +
                '<span class="num ' + tools.addMinusClass(annu_yield) + '">' + annu_yield + '</span>' +
                '<span>%</span>' + recommend_info_str +
                '</div>';
        } else if (data.prod_sub_type == "20") {
            //年化业绩基准
            var preincomerate = data.preincomerate;
            if (preincomerate != "--") {
                preincomerate = (+preincomerate).toFixed(2);
            }

            //起购
            var threshold_amount = data.threshold_amount;
            //期限(天)
            var maturity_days = data.maturity_days;

            html += '<div class="balance">' +
                '<span>年化业绩基准：</span>' +
                '<span class="num ' + tools.addMinusClass(preincomerate) + '">' + preincomerate + '</span>' +
                '<span>%</span>' +
                '</div>' +
                '<div class="text">' +
                '<span class="purchase">起购：' + tools.fmoney(threshold_amount) + '元</span>' +
                '<span class="deposit">期限：' + maturity_days + '</span>' + recommend_info_str +
                '</div>';
        } else if (data.prod_sub_type == "30") {
            var threshold_amount = data.threshold_amount;
            if (threshold_amount != "--") {
                threshold_amount = tools.fmoney(threshold_amount) + "元";
            }
            var p_expected_yield = data.p_expected_yield;
            if (p_expected_yield != "--") {
                p_expected_yield = tools.fmoney(p_expected_yield + "");
            }

            html += '<div class="balance">' +
                '<span>近一月年化：</span>' +
                '<span class="num ' + tools.addMinusClass(p_expected_yield) + '">' + p_expected_yield + '</span>' +
                '<span>%</span>' +
                '</div>' +
                '<div class="text">' +
                '<span class="purchase">起购：' + threshold_amount + '</span>' +
                '<span class="deposit">建议持有30天以上</span>' + recommend_info_str +
                '</div>';
        } else if (data.prod_sub_type == "50" || (data.prod_sub_type == "--")) {
            html += renderBond(data);

        }

        var buy_state = data.buy_state;
        var buy_state_name = "";
        var btnClass = "";
        //1-购买  2-预约 3-敬请期待  4-售罄
        if (buy_state == "1") {
            buy_state_name = "购买";
            btnClass = "";
        }
        if (buy_state == "2") {
            buy_state_name = "预约";
            btnClass = "";
        }
        if (buy_state == "3") {
            buy_state_name = "敬请期待";
            btnClass = "";
        }
        if (buy_state == "4") { //其他产品为售罄
            buy_state_name = "封闭中";
            btnClass = "sold_out";
        }
        if (buy_state == "5") { //其他产品为售罄
            buy_state_name = "售罄";
            btnClass = "sold_out";
        }
        if (buy_state == "6") { //定制产品买入置灰
            buy_state_name = "购买";
            btnClass = "";
        }

        html += '<div class="btn ' + btnClass + '">' + buy_state_name + '</div>' +
            '</div>';
        return html;
    }

    //手势密码 控件
    function mobilePhoneControl() {
        if (platform == "0") { //浏览器端
            return;
        }
        if (!ut.getUserInf()) { //未登录
            return;
        }
        if (appUtils.getPageParam("bindCard")) { //绑卡流程进入首页
            return;
        }
        var gesture_code_data = common.getLocalStorage("gesture_code");
        if (gesture_code_data == "1") { //首页已弹过
            return;
        }
        //存储首页是否弹过手势密码  1 已弹过
        common.setLocalStorage("gesture_code", "1");
        var account = common.getLocalStorage("mobileWhole");
        appUtils.setSStorageInfo("isCanBack", "0");
        var setParam = {
            "funcNo": "50264", //设置手势密码的设置状态
            "moduleName": "mall",
            "flag": 1, //flag	String	状态（0：取消手势，1：设置手势，2：修改手势）
            "style": "1", //style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
            "account": account,
            "isCanBack": "1",
            "position": "1",
            "errorNum": "5"
        };
        external.callMessage(setParam);
    }

    //渲染债基
    function renderBond(data) {
        var html = "";
        var recommend_info = data.recommend_info;
        var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
        if (data.prod_sub_type2 == "01") { //定开
            var dk_income_rate = data.dk_income_rate;
            var per_yield = data.per_yield;
            html += '<div class="balance">' +
                '<span>上一封闭期年化收益率：</span>' +
                '<span class="num ' + tools.addMinusClass(per_yield) + '">' + tools.fmoney(per_yield) + '</span>' +
                '<span>%</span>' +
                '</div>' +
                '<div class="text">' +
                '<span class="purchase">起购：' + tools.fmoney(data.threshold_amount) + '元</span>' +
                '<span class="deposit">封闭期：' + data.maturity_days + '</span>' + recommend_info_str +
                '</div>';
        } else if (data.prod_sub_type2 == "52") {
            var dk_income_rate = data.dk_income_rate;
            html += '<div class="balance">' +
                '<span>' + data.income_period_type_desc + '年化：</span>' +
                '<span class="num ' + tools.addMinusClass(dk_income_rate) + '">' + tools.fmoney(dk_income_rate) + '</span>' +
                '<span>%</span>' +
                '</div>' +
                '<div class="text">' +
                '<span class="purchase">起购：' + tools.fmoney(data.threshold_amount) + '元</span>' +
                '<span class="deposit">锁定期：' + data.maturity_days + '</span>' + recommend_info_str +
                '</div>';
        } else {
            //空数据处理
            html += '<div class="text">' +
                '<span class="purchase">单位净值：' + tools.fmoney(data.nav) + '</span>' +
                '<span class="deposit">年涨幅：' + tools.fmoney(data.year_rate) + '</span>' + recommend_info_str +
                '</div>';

        }
        return html;
    }

    function renderExclusiveBank(product) {
        var img_str = product.prod_img_url ? '<img src="' + global.oss_url + product.prod_img_url + '">' : "";
        var html = "<div class='item'><div class='box'>" +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(product) + "</span>" +
            "<p class='title'><span>" + product.prod_exclusive_name + "</span>" + img_str + "</p>" +
            "<div class='balance'><span>综合利率：<span class='num'>" + tools.fmoney(product.rate) + "</span>%</span></div>" +
            "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + tools.fmoney(product.prod_per_min_amt) + "元</span><span>每" + product.increase_term + "天付息</span></p>" +
            "</p><div  class='btn " + exclusive_buy_state[product.buy_state].btnClass + " in'>" + exclusive_buy_state[product.buy_state].btnText + "</div></div></div>";
        return html;
    }

    function renderExclusiveBond(product) {
        var img_str = product.prod_img_url ? '<img src="' + global.oss_url + product.prod_img_url + '">' : "";
        var html = '<div class="item">' +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(product) + "</span>" +
            '<p class="title"><span>' + product.prod_exclusive_name + '</span>' + img_str + '</p>' +
            '<div class="balance">' +
            '<span>年化业绩基准：</span>' +
            '<span class="num ' + tools.addMinusClass(product.rate) + '">' + tools.fmoney(product.rate) + '</span>' +
            '<span>%</span>' +
            '</div>' +
            '<div class="text">' +
            '<span class="purchase">起购：' + tools.fmoney(product.prod_per_min_amt) + '元</span>' +
            '<span class="deposit">期限：' + product.increase_term + '天</span>' +
            '</div>' +
            '<div class="btn ' + exclusive_buy_state[product.buy_state].btnClass + '">' + exclusive_buy_state[product.buy_state].btnText + '</div>' +
            '</div>';
        return html;
    }

    function renderExclusiveMoney(product) {
        var img_str = product.prod_img_url ? '<img src="' + global.oss_url + product.prod_img_url + '">' : "";
        var html = '<div class="item">' +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(product) + "</span>" +
            '<p class="title"><span>' + product.prod_exclusive_name + '</span>' + img_str + '</p>' +
            '<div class="balance">' +
            '<span>综合收益率：</span>' +
            '<span class="num ' + tools.addMinusClass(product.rate) + '">' + tools.fmoney(product.rate) + '</span>' +
            '<span>%</span>' +
            '</div>' +
            '<div class="text">' +
            '<span class="purchase">起购：' + tools.fmoney(product.prod_per_min_amt) + '元</span>' +
            '<span class="deposit">期限：' + product.increase_term + '天</span>' +
            '</div>' +
            '<div class="btn ' + exclusive_buy_state[product.buy_state].btnClass + '">' + exclusive_buy_state[product.buy_state].btnText + '</div>' +
            '</div>';
        return html;
    }

    function renderExclusiveHold(product) {
        var img_str = product.prod_img_url ? '<img src="' + global.oss_url + product.prod_img_url + '">' : "";
        var html = '<div class="item">' +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(product) + "</span>" +
            '<p class="title"><span>' + product.prod_exclusive_name + '</span>' + img_str + '</p>' +
            '<div class="balance">' +
            '<span>' + product.income_period_type_desc + '年化：</span>' +
            '<span class="num ' + tools.addMinusClass(product.dk_income_rate) + '">' + tools.fmoney(product.dk_income_rate) + '</span>' +
            '<span>%</span>' +
            '</div>' +
            '<div class="text">' +
            '<span class="purchase">起购：' + tools.fmoney(product.prod_per_min_amt) + '元</span>' +
            '<span class="deposit">锁定期：' + product.p_inrest_term + '</span>' +
            '</div>' +
            '<div class="btn ' + exclusive_buy_state[product.buy_state].btnClass + '">' + exclusive_buy_state[product.buy_state].btnText + '</div>' +
            '</div>';
        return html;
    }
    //住房专享
    function renderExclusiveHousing(product) {
        var img_str = product.prod_img_url ? '<img src="' + global.oss_url + product.prod_img_url + '">' : "";
        var prod_per_min_amt = product.prod_per_min_amt >= 10000 ? (product.prod_per_min_amt / 10000 + "万") : tools.fmoney(product.prod_per_min_amt);
        var html = '<div class="item">' +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(product) + "</span>" +
            '<p class="title"><span>' + product.prod_exclusive_name + '</span>' + img_str + '</p>' +
            '<div class="balance">' +
            '<span>七日年化：</span>' +
            '<span class="num ' + tools.addMinusClass(product.annu_yield) + '">' + tools.fmoney(product.annu_yield) + '</span>' +
            '<span>%</span>' +
            '</div>' +
            '<div class="text">' +
            '<span class="purchase">投资金额：' + prod_per_min_amt + '元</span>' +
            '</div>' +
            '<div class="btn ' + exclusive_buy_state[product.buy_state].btnClass + '">' + exclusive_buy_state[product.buy_state].btnText + '</div>' +
            '</div>';
        return html;
    }

    function destroy() {
        $(_pageId + " #loginOut").text('');
        if (hIscroll) {
            hIscroll.destroy();
            hIscroll = null;
        }
        $(_pageId).hide();
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .pop_gold").hide();
        $(_pageId + " .gold_inner #tyj_money").html("");
        $(_pageId + " .tip").hide();
        $(_pageId + " .inclusive_area").hide();
        $(_pageId + " .thfund_area").hide();
        $(_pageId + " .bank_area").hide();
        $(_pageId + ' .highEnd_area .more').hide();
        $(_pageId + " .highEnd_area").hide();
        $(_pageId + ' .highEnd_list').html('')
        $(_pageId + " .exclusive").hide();
        $(_pageId + " #yuanhui").hide();

    }

    var userIndexs = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = userIndexs;
})
    ;
