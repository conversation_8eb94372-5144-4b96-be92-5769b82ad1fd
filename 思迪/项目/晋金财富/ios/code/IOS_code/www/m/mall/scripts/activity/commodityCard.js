//七鱼客服web页面
define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var tools = require("../common/tools"); 
    var service = require("mobileService");
    var _pageCode = "activity/commodityCard", _pageId = "#activity_commodityCard";
    function init() {
        //初始化页面
        setData()
    }
    async function setData(){
        let arr = await getFirstList();
        let newArr = [...arr[1].data['02'],...arr[1].data['03'],...arr[1].data['04']]
        console.log(newArr)
        let html = ''
        for(let i = 0; i < newArr.length ; i++){
            let productInfoSon = JSON.stringify(newArr[i]) //二级列表接口传递数据
            html += `<div class="card" style="overflow: hidden">
                        <em style='display: none' class='productInfo'>${productInfoSon}</em>
                        <h5>${newArr[i].prod_sname}</h5>
                        <ul>
                            <li>
                                <span style="color: red">${ (newArr[i].preincomerate ? tools.fmoney(newArr[i].preincomerate) + "%" : "--") }</span>
                                <span>成立以来年化</span>
                            </li>
                            <li>
                                <span>${newArr[i].inrest_term ? newArr[i].inrest_term : '--'}</span>
                                <span>锁定期</span>
                            </li>
                            <li>
                                <span>${newArr[i].threshold_amount}元</span>
                                <span>起购金额</span>
                            </li>
                        </ul>
                        <div class="label">
                            <span>中风险R3</span>
                            <span class="btn">发送</span>
                        </div>
                    </div>`
        }
        $(_pageId + " .list").html(html)
    }
    //绑定事件
    function bindPageEvent(){
        appUtils.preBindEvent($(_pageId + " .list"), ".card", function () {
            var productInfo = JSON.parse($(this).find("em").text()); //存储数据格式
            var data = {
                // 要发送到七鱼的商品或者订单的数据对象
                picture:'https://jjdx.sxjjd.com/m/mall/images/icon_app.png',
                // url:'template/publicOfferingDetail?fund_code=' + productInfo.fund_code + '&prod_sub_type2=' + productInfo.prod_sub_type2,
                showCustomMsg:'1',
                title:productInfo.prod_sname,
                desc:'点击查看产品详情',
            }
            if(productInfo.prod_sub_type2 == '100'){
                data.desc = "点击查看产品详情"
                data.url = 'template/heighEndProduct?fund_code=' + productInfo.fund_code + '&prod_sub_type2=' + productInfo.prod_sub_type2;
            }else{
                data.url = 'template/publicOfferingDetail?fund_code=' + productInfo.fund_code + '&prod_sub_type2=' + productInfo.prod_sub_type2;
                data.desc = "成立以来年化:" + (productInfo.preincomerate?tools.fmoney(productInfo.preincomerate) + '%' : "--") + " 锁定期:" + (productInfo.inrest_term?productInfo.inrest_term:"") + " 起购金额:" + (productInfo.threshold_amount?productInfo.threshold_amount+'元':"")
            }
            window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
        }, 'click');
    }
    function destroy(){

    }
    function pageBack(){
        appUtils.pageBack();
    }
    //获取一级列表分类
    async function getFirstList(){
        return new Promise(async(resolve, reject) => {
            service.reqFun102117({}, async(data) =>{
                if(data.error_no == '0'){
                    var res = data.results
                    resolve(res)
                }else{
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
        
    }
    var commodityCard = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = commodityCard;
});
// /**
//  * 模块名：新理念抢先猜
//  */
// define(function (require, exports, module) {
//     /* 引用模块 */
//     var appUtils = require("appUtils"), layerUtils = require("layerUtils"), SHIscroll = require("shIscroll"),
//         service = require("mobileService"), gconfig = require("gconfig"), common = require("common"), validatorUtil = require("validatorUtil");
//     var external = require("external");
//     var ut = require("../common/userUtil");
//     var swiper = require("../common/swiper");
//     /* 常量 */
//     var _pageCode = "activity/commodityCard", _pageId = "#activity_commodityCard";
//     /**
//      * 初始化
//      */
//     function init() {

//     }

//     /**
//      * 事件绑定
//      */
//     function bindPageEvent() {

//     }
//     //发送
//     appUtils.bindEvent($(_pageId + " .label .btn0"), function () {
//         var data = {
//             // 要发送到七鱼的商品或者订单的数据对象
//             template:'pictureLink',
//             picture:'https://jjdx.sxjjd.com/m/mall/images/icon_app.png',
//             url:'template/publicOfferingDetail',
//             showCustomMsg:'1',
//         }
//         window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
//         console.log('11')
//     });
//     //发送
//     appUtils.bindEvent($(_pageId + " .label .btn1"), function () {
//         var data = {
//             // 要发送到七鱼的商品或者订单的数据对象
//             picture:'https://jjdx.sxjjd.com/m/mall/images/icon_app.png',
//             url:'template/publicOfferingDetail',
//             showCustomMsg:'1',
//             title:'晋金宝90天',
//             desc:'晋金宝90天简介',
//             activity:'活动展示标题',
//             activityHref:'活动跳转链接',
//             price:'1.00'
//         }
//         window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
//         console.log('11')
//     });

//     //发送
//     appUtils.bindEvent($(_pageId + " .label .btn2"), function () {
//         var data = {
//             // 要发送到七鱼的商品或者订单的数据对象
//             picture:'https://jjdx.sxjjd.com/m/mall/images/icon_app.png',
//             url:'template/publicOfferingDetail',
//             showCustomMsg:'1',
//             title:'晋金宝90天',
//             desc:'晋金宝90天简介',
//             activity:'活动展示标题',
//             activityHref:'活动跳转链接',
//             payMoney:'1.00',
//             orderId:"订单id",
//             orderTime:"2021-10-11",
//             orderSku:"订单描述",
//             orderCount:"1",
//             orderStatus:"成功",
//             tags:'{"label": "打开七鱼网址","url": "https://qi.163.com"}',
//         }
//         window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
//         console.log('11')
//     });

//     //发送
//     appUtils.bindEvent($(_pageId + " .label .btn0"), function () {
//         var data = {
//             // 要发送到七鱼的商品或者订单的数据对象
//             template:'pictureLink',
//             picture:'../mall/images/activity/phone.png',
//             url:'template/publicOfferingDetail',
//             showCustomMsg:'1',
//         }
//         window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
//         console.log('11')
//     });

//     /**
//      * 销毁
//      */
//     function destroy() {

//     };
//     /*
//      * 返回
//      */
//     function pageBack() {
//         appUtils.pageBack();
//     }

//     var index = {
//         "init": init,
//         "bindPageEvent": bindPageEvent,
//         "destroy": destroy,
//         "pageBack": pageBack
//     };
//     module.exports = index;


// });
