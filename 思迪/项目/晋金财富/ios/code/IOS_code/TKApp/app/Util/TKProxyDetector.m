//
//  TKProxyDetector.m
//  TKApp_H5SHARE
//
//  Created by <PERSON> on 2025/7/29.
//  Copyright © 2025 liubao. All rights reserved.
//

#import "TKProxyDetector.h"
#import <ifaddrs.h>
#import <arpa/inet.h>
#import <net/if.h>
#import <SystemConfiguration/SystemConfiguration.h>
#import <TKAppBase_V2/TKLog.h>
#import <TKAppBase_V2/TKStringHelper.h>
#import <TKAppBase_V2/TKNetHelper.h>
// NetworkExtension import 已移除，避免NEVPNManager权限问题

#pragma mark - 常量定义

// 代理类型名称常量
static NSString * const kProxyTypeNameHTTP = @"HTTP代理";
static NSString * const kProxyTypeNameSystem = @"系统代理";
static NSString * const kProxyTypeNameVPNInterface = @"VPN连接";
static NSString * const kProxyTypeNameSystemVPN = @"系统VPN";
static NSString * const kProxyTypeNameApplication = @"代理应用";

// 网络接口类型常量
static NSString * const kInterfaceTypePPP = @"PPP";
static NSString * const kInterfaceTypeTAP = @"TAP";
static NSString * const kInterfaceTypeTUN = @"TUN";
static NSString * const kInterfaceTypeUTUN = @"UTUN";
static NSString * const kInterfaceTypeIPSEC = @"IPSEC";
static NSString * const kInterfaceTypeOTHER = @"OTHER";

// 网络接口前缀常量
static NSString * const kInterfacePrefixPPP = @"ppp";
static NSString * const kInterfacePrefixTAP = @"tap";
static NSString * const kInterfacePrefixTUN = @"tun";
static NSString * const kInterfacePrefixUTUN = @"utun";
static NSString * const kInterfacePrefixIPSEC = @"ipsec";
static NSString * const kInterfacePrefixEN = @"en";
static NSString * const kInterfacePrefixPDP = @"pdp_ip";

// 本地地址常量
static NSString * const kLocalhost127 = @"127.";
static NSString * const kLocalhostName = @"localhost";
static NSString * const kLocalhost0 = @"0.0.0.0";

// VPN检测阈值常量 - 统一UTUN接口分类标准
static const NSInteger kUTUNSystemThreshold = 4;      // 系统utun接口阈值 (utun0-4)
static const NSInteger kUTUNSuspiciousThreshold = 5;   // 可疑utun接口阈值 (utun5-9)
static const NSInteger kUTUNUserThreshold = 10;       // 用户utun接口阈值 (utun10+)
static const NSInteger kActiveInterfaceThreshold = 15; // 活跃接口异常阈值
static const NSInteger kSystemUTUNThreshold = 8;      // 系统utun接口异常阈值

#pragma mark - 数据结构

// 网络接口信息结构
typedef struct {
    NSString *name;
    BOOL isActive;
    BOOL isVPNCandidate;
    NSString *interfaceType;
    uint32_t ipAddress;
    NSString *ipString;
} TKNetworkInterfaceInfo;

// 网络接口枚举回调
typedef void(^TKNetworkInterfaceEnumerator)(TKNetworkInterfaceInfo *info, BOOL *stop);

// 统一网络分析结果结构
typedef struct {
    // VPN接口检测结果
    BOOL hasDefiniteVPN;
    NSString *definiteVPNInfo;

    // 网络接口特征分析结果
    int totalInterfaces;
    int activeInterfaces;
    int wifiInterfaces;
    int cellularInterfaces;
    int systemUtunInterfaces;
    int suspiciousInterfaces;
    NSArray *interfaceDetails;
    BOOL hasSystemVPN;

    // 用户VPN验证结果
    BOOL hasRealUserVPN;
} TKUnifiedNetworkAnalysisResult;

@implementation TKProxyDetector

+ (instancetype)sharedDetector {
    static TKProxyDetector *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[TKProxyDetector alloc] init];
    });
    return instance;
}

- (TKProxyDetectionResult *)detectAllProxyTypes {
    TKLogDebug(@"开始完整代理检测");
    return [self performProxyDetection];
}

/**
 * 统一的代理检测核心逻辑（同步执行，优化版本）
 * @return 检测结果
 */
- (TKProxyDetectionResult *)performProxyDetection {
    TKProxyDetectionResult *result = [[TKProxyDetectionResult alloc] init];
    NSMutableArray *typeNames = [NSMutableArray array];
    TKProxyType detectedTypes = TKProxyTypeNone;

    // 1. HTTP代理检测（同步，快速）
    if ([self detectHTTPProxy]) {
        detectedTypes |= TKProxyTypeHTTP;
        [typeNames addObject:kProxyTypeNameHTTP];
        result.httpProxyInfo = [self getHTTPProxyInfo];
        TKLogDebug(@"检测到HTTP代理: %@", result.httpProxyInfo);
    }

    // 2. 系统代理检测（同步，快速）
    if ([self detectSystemProxySettings]) {
        detectedTypes |= TKProxyTypeSystem;
        [typeNames addObject:kProxyTypeNameSystem];
        TKLogDebug(@"检测到系统代理配置");
    }

    // 3. 统一网络接口分析（一次性完成VPN和系统VPN检测）
    TKUnifiedNetworkAnalysisResult networkResult = [self performUnifiedNetworkAnalysis];

    // 3.1 VPN接口检测结果
    if (networkResult.hasDefiniteVPN) {
        detectedTypes |= TKProxyTypeVPNInterface;
        [typeNames addObject:kProxyTypeNameVPNInterface];
        result.vpnInterfaceInfo = networkResult.definiteVPNInfo;
        TKLogDebug(@"检测到VPN接口: %@", result.vpnInterfaceInfo);
    }

    // 3.2 系统VPN检测结果
    if (networkResult.hasSystemVPN) {
        detectedTypes |= TKProxyTypeSystemVPN;
        [typeNames addObject:kProxyTypeNameSystemVPN];
        TKLogDebug(@"检测到系统VPN配置");
    }

    // 4. 代理应用检测（同步执行）
    BOOL hasProxyApp = [self detectProxyApplications];
    if (hasProxyApp) {
        detectedTypes |= TKProxyTypeApplication;
        [typeNames addObject:kProxyTypeNameApplication];
        TKLogDebug(@"检测到代理应用");
    }

    // 完成检测结果处理
    result.detectedTypes = detectedTypes;
    result.typeNames = [typeNames copy];
    result.hasProxy = (detectedTypes != TKProxyTypeNone);

    NSString *resultMessage = result.hasProxy ? @"检测到代理" : @"未检测到代理";
    TKLogDebug(@"代理检测完成，结果: %@", resultMessage);
    TKLogDebug(@"检测到的代理类型: %@", [result.typeNames componentsJoinedByString:@","]);

    return result;
}

- (BOOL)detectHTTPProxy {
    TKLogDebug(@"开始HTTP代理检测");
    NSString *httpProxy = [self getHTTPProxyInfo];
    BOOL hasProxy = [TKStringHelper isNotEmpty:httpProxy];

    return hasProxy;
}

- (nullable NSString *)getHTTPProxyInfo {
    NSString *httpProxy = [TKNetHelper fetchHttpProxy];

    if ([TKStringHelper isNotEmpty:httpProxy]) {
        // 排除本地代理地址，避免与代理应用检测重复
        // 本地代理应该由 detectProxyApplications 方法检测
        if ([self isLocalProxyAddress:httpProxy]) {
            TKLogDebug(@"检测到本地代理地址 %@，归类为代理应用而非外部HTTP代理", httpProxy);
            return nil;
        }

        TKLogDebug(@"检测到外部HTTP代理: %@", httpProxy);
        return httpProxy;
    }

    TKLogDebug(@"未检测到HTTP代理");
    return nil;
}

/**
 * 判断是否为本地代理地址
 */
- (BOOL)isLocalProxyAddress:(NSString *)address {
    return ([address hasPrefix:kLocalhost127] ||
            [address hasPrefix:kLocalhostName] ||
            [address isEqualToString:kLocalhost0]);
}

/**
 * 统一的网络接口分析方法（优化版本）
 * 在单次枚举中完成所有网络接口检测，避免重复系统调用
 */
- (TKUnifiedNetworkAnalysisResult)performUnifiedNetworkAnalysis {
    TKLogDebug(@"开始统一网络接口分析");

    TKUnifiedNetworkAnalysisResult result = {0};
    result.interfaceDetails = [NSMutableArray array];
    NSMutableArray *interfaceDetails = (NSMutableArray *)result.interfaceDetails;

    struct ifaddrs *interfaces = NULL;
    if (getifaddrs(&interfaces) != 0) {
        TKLogDebug(@"无法获取网络接口信息");
        return result;
    }

    struct ifaddrs *temp_addr = interfaces;
    while (temp_addr != NULL) {
        TKNetworkInterfaceInfo info = {0};
        info.name = [NSString stringWithUTF8String:temp_addr->ifa_name];

        // 判断接口是否活跃
        info.isActive = (temp_addr->ifa_addr &&
                        temp_addr->ifa_flags & IFF_UP &&
                        temp_addr->ifa_flags & IFF_RUNNING &&
                        !(temp_addr->ifa_flags & IFF_LOOPBACK));

        // 获取IP地址信息
        if (temp_addr->ifa_addr && temp_addr->ifa_addr->sa_family == AF_INET) {
            struct sockaddr_in *addr_in = (struct sockaddr_in *)temp_addr->ifa_addr;
            info.ipAddress = ntohl(addr_in->sin_addr.s_addr);
            info.ipString = [NSString stringWithFormat:@"%d.%d.%d.%d",
                           (info.ipAddress >> 24) & 0xFF, (info.ipAddress >> 16) & 0xFF,
                           (info.ipAddress >> 8) & 0xFF, info.ipAddress & 0xFF];
        }

        result.totalInterfaces++;

        if (info.isActive) {
            result.activeInterfaces++;
            [interfaceDetails addObject:info.name];

            // 1. 检测明确的VPN接口（用于detectVPNInterface）
            if (!result.hasDefiniteVPN) {
                NSString *interfaceType = @"";
                BOOL isDefiniteVPN = [self isDefiniteVPNInterface:info.name interfaceType:&interfaceType];
                TKLogDebug(@"接口 %@ - 类型判断: %@, IP: %@, 有效IP: %@",
                          info.name,
                          isDefiniteVPN ? [NSString stringWithFormat:@"明确VPN(%@)", interfaceType] : @"非VPN",
                          info.ipString ?: @"无IP",
                          [self isValidNonLocalIP:info.ipAddress] ? @"是" : @"否");

                if (isDefiniteVPN && [self isValidNonLocalIP:info.ipAddress]) {
                    result.hasDefiniteVPN = YES;
                    result.definiteVPNInfo = [NSString stringWithFormat:@"%@(%@)", info.name, interfaceType];
                    TKLogDebug(@"✅ 确认明确VPN接口: %@ (%@) - IP: %@", info.name, interfaceType, info.ipString);
                }
            }

            // 2. 网络接口特征分析
            if ([info.name hasPrefix:kInterfacePrefixEN]) {
                result.wifiInterfaces++;
                TKLogDebug(@"WiFi接口: %@ - IP: %@", info.name, info.ipString ?: @"无IP");
            } else if ([info.name hasPrefix:kInterfacePrefixPDP]) {
                result.cellularInterfaces++;
                TKLogDebug(@"蜂窝接口: %@ - IP: %@", info.name, info.ipString ?: @"无IP");
            } else if ([info.name hasPrefix:kInterfacePrefixUTUN]) {
                NSInteger interfaceNumber = [self extractUTUNInterfaceNumber:info.name];
                TKLogDebug(@"UTUN接口: %@ (编号: %ld) - IP: %@", info.name, (long)interfaceNumber, info.ipString ?: @"无IP");

                if (interfaceNumber <= kUTUNSystemThreshold) {
                    result.systemUtunInterfaces++;  // utun0-4: 系统接口
                    TKLogDebug(@"  -> 系统UTUN接口 (utun0-4)");
                } else if (interfaceNumber < kUTUNUserThreshold) {
                    result.suspiciousInterfaces++;  // utun5-9: 可疑接口，需要深度验证
                    TKLogDebug(@"  -> 可疑VPN接口 (utun5-9)，需要IP验证");

                    // 3. 同时进行用户VPN验证（utun5-9需要IP验证）
                    BOOL validIP = [self isValidNonLocalIP:info.ipAddress];
                    BOOL vpnIPRange = [self isUserVPNIPRange:info.ipAddress];
                    TKLogDebug(@"  -> IP验证: 有效IP=%@, VPN范围=%@", validIP ? @"是" : @"否", vpnIPRange ? @"是" : @"否");

                    if (!result.hasRealUserVPN && validIP && vpnIPRange) {
                        result.hasRealUserVPN = YES;
                        TKLogDebug(@"  -> ✅ 可疑utun接口验证为用户VPN: %@ - IP: %@", info.name, info.ipString);
                    }
                } else {
                    TKLogDebug(@"  -> 用户VPN接口 (utun10+)，由明确VPN检测处理");
                }
            } else {
                // 检查其他VPN候选接口类型
                NSString *interfaceType = @"";
                BOOL isVPNCandidate = [self isVPNCandidateInterface:info.name interfaceType:&interfaceType];
                TKLogDebug(@"其他接口: %@ - 类型: %@, VPN候选: %@, IP: %@",
                          info.name,
                          interfaceType.length > 0 ? interfaceType : @"未知",
                          isVPNCandidate ? @"是" : @"否",
                          info.ipString ?: @"无IP");

                if (isVPNCandidate) {
                    result.suspiciousInterfaces++;
                    TKLogDebug(@"  -> 发现VPN候选接口");

                    // 非utun接口的用户VPN验证
                    BOOL validIP = [self isValidNonLocalIP:info.ipAddress];
                    if (!result.hasRealUserVPN && validIP) {
                        result.hasRealUserVPN = YES;
                        TKLogDebug(@"  -> ✅ 确认真实用户VPN接口: %@ (%@) - IP: %@", info.name, interfaceType, info.ipString);
                    }
                }
            }
        }

        temp_addr = temp_addr->ifa_next;
    }

    freeifaddrs(interfaces);

    // 4. 系统VPN判断逻辑
    if (result.suspiciousInterfaces > 0) {
        if (result.hasRealUserVPN) {
            result.hasSystemVPN = YES;
            TKLogDebug(@"验证确认存在用户VPN接口，判定为系统VPN");
        } else {
            TKLogDebug(@"可疑接口验证失败，可能是系统保留接口");
        }
    } else if (result.activeInterfaces > kActiveInterfaceThreshold &&
               result.systemUtunInterfaces > kSystemUTUNThreshold) {
        result.hasSystemVPN = YES;
        TKLogDebug(@"活跃接口和系统utun接口数量严重异常，可能存在系统VPN");
    } else {
        TKLogDebug(@"网络接口特征正常，未检测到系统VPN");
    }

    // 统一日志输出
    TKLogDebug(@"网络接口统计 - 总数: %d, 活跃: %d, WiFi: %d, 蜂窝: %d, 系统utun: %d, 可疑: %d",
              result.totalInterfaces, result.activeInterfaces, result.wifiInterfaces,
              result.cellularInterfaces, result.systemUtunInterfaces, result.suspiciousInterfaces);
    TKLogDebug(@"活跃接口详情: %@", [interfaceDetails componentsJoinedByString:@", "]);
    TKLogDebug(@"统一网络接口分析完成");

    return result;
}

/**
 * 通用网络接口枚举方法（保留用于向后兼容）
 */
- (void)enumerateNetworkInterfaces:(TKNetworkInterfaceEnumerator)enumerator {
    struct ifaddrs *interfaces = NULL;

    if (getifaddrs(&interfaces) != 0) {
        return;
    }

    struct ifaddrs *temp_addr = interfaces;
    while (temp_addr != NULL) {
        TKNetworkInterfaceInfo info = {0};
        info.name = [NSString stringWithUTF8String:temp_addr->ifa_name];

        // 判断接口是否活跃
        info.isActive = (temp_addr->ifa_addr &&
                        temp_addr->ifa_flags & IFF_UP &&
                        temp_addr->ifa_flags & IFF_RUNNING &&
                        !(temp_addr->ifa_flags & IFF_LOOPBACK));

        // 移除默认的VPN候选判断，避免重复检测和日志打印
        // 让调用方法自行进行接口类型判断
        info.isVPNCandidate = NO;
        info.interfaceType = @"";

        // 获取IP地址信息
        if (temp_addr->ifa_addr && temp_addr->ifa_addr->sa_family == AF_INET) {
            struct sockaddr_in *addr_in = (struct sockaddr_in *)temp_addr->ifa_addr;
            info.ipAddress = ntohl(addr_in->sin_addr.s_addr);
            info.ipString = [NSString stringWithFormat:@"%d.%d.%d.%d",
                           (info.ipAddress >> 24) & 0xFF, (info.ipAddress >> 16) & 0xFF,
                           (info.ipAddress >> 8) & 0xFF, info.ipAddress & 0xFF];
        }

        BOOL stop = NO;
        enumerator(&info, &stop);
        if (stop) break;

        temp_addr = temp_addr->ifa_next;
    }

    freeifaddrs(interfaces);
}

/**
 * 判断接口是否为明确的VPN接口（用于detectVPNInterface）
 * 只包含明确的VPN接口类型，排除可疑的utun5-9
 */
- (BOOL)isDefiniteVPNInterface:(NSString *)interfaceName interfaceType:(NSString **)interfaceType {
    // PPP接口检测
    if ([interfaceName hasPrefix:kInterfacePrefixPPP]) {
        *interfaceType = kInterfaceTypePPP;
        return YES;
    }

    // TAP接口检测
    if ([interfaceName hasPrefix:kInterfacePrefixTAP]) {
        *interfaceType = kInterfaceTypeTAP;
        return YES;
    }

    // TUN接口检测（排除UTUN）
    if ([interfaceName hasPrefix:kInterfacePrefixTUN] &&
        ![interfaceName hasPrefix:kInterfacePrefixUTUN]) {
        *interfaceType = kInterfaceTypeTUN;
        return YES;
    }

    // UTUN接口检测 - 只检测明确的用户VPN接口（utun10+）
    if ([interfaceName hasPrefix:kInterfacePrefixUTUN]) {
        NSInteger interfaceNumber = [self extractUTUNInterfaceNumber:interfaceName];
        // 职责分离：只检测明确的用户VPN接口（utun10+）
        if (interfaceNumber >= kUTUNUserThreshold) {
            *interfaceType = kInterfaceTypeUTUN;
            return YES;
        }
    }

    // IPSec接口检测
    if ([interfaceName hasPrefix:kInterfacePrefixIPSEC]) {
        *interfaceType = kInterfaceTypeIPSEC;
        return YES;
    }

    *interfaceType = kInterfaceTypeOTHER;
    return NO;
}

/**
 * 判断接口是否为VPN候选（用于analyzeNetworkInterfaceCharacteristics）
 * 包含所有可能的VPN接口类型，用于深度分析
 */
- (BOOL)isVPNCandidateInterface:(NSString *)interfaceName interfaceType:(NSString **)interfaceType {
    // PPP接口检测
    if ([interfaceName hasPrefix:kInterfacePrefixPPP]) {
        *interfaceType = kInterfaceTypePPP;
        return YES;
    }

    // TAP接口检测
    if ([interfaceName hasPrefix:kInterfacePrefixTAP]) {
        *interfaceType = kInterfaceTypeTAP;
        return YES;
    }

    // TUN接口检测（排除UTUN）
    if ([interfaceName hasPrefix:kInterfacePrefixTUN] &&
        ![interfaceName hasPrefix:kInterfacePrefixUTUN]) {
        *interfaceType = kInterfaceTypeTUN;
        return YES;
    }

    // UTUN接口检测 - 包含可疑和用户VPN接口（utun5+）
    if ([interfaceName hasPrefix:kInterfacePrefixUTUN]) {
        NSInteger interfaceNumber = [self extractUTUNInterfaceNumber:interfaceName];
        // 深度分析：包含可疑接口（utun5+），后续由精筛方法进一步分类
        if (interfaceNumber >= kUTUNSuspiciousThreshold) {
            *interfaceType = kInterfaceTypeUTUN;
            return YES;
        }
    }

    // IPSec接口检测
    if ([interfaceName hasPrefix:kInterfacePrefixIPSEC]) {
        *interfaceType = kInterfaceTypeIPSEC;
        return YES;
    }

    *interfaceType = kInterfaceTypeOTHER;
    return NO;
}

/**
 * 提取UTUN接口编号
 */
- (NSInteger)extractUTUNInterfaceNumber:(NSString *)interfaceName {
    if (![interfaceName hasPrefix:kInterfacePrefixUTUN] || interfaceName.length <= 4) {
        return -1;
    }

    NSString *numberPart = [interfaceName substringFromIndex:4];
    return [numberPart integerValue];
}

/**
 * 统一的IP地址验证方法
 */
- (BOOL)isValidNonLocalIP:(uint32_t)ip {
    // IP地址为0
    if (ip == 0) return NO;

    // 127.x.x.x (回环地址)
    if ((ip & 0xFF000000) == 0x7F000000) return NO;

    // 169.254.x.x (APIPA自动私有IP地址)
    if ((ip & 0xFFFF0000) == 0xA9FE0000) return NO;

    // *************** (广播地址)
    if (ip == 0xFFFFFFFF) return NO;

    return YES;
}

/**
 * 判断IP是否为用户VPN范围
 * 使用更清晰的位运算常量，支持已知VPN服务商IP段
 */
- (BOOL)isUserVPNIPRange:(uint32_t)ip {
    // 1. 检查RFC 1918私有网络范围

    // 10.x.x.x 私有网络范围 (排除系统常用的10.0.x.x和10.1.x.x)
    if ((ip & 0xFF000000) == 0x0A000000) {
        // 排除10.0.x.x和10.1.x.x，这些通常是系统保留
        if ((ip & 0xFFFF0000) != 0x0A000000 && (ip & 0xFFFF0000) != 0x0A010000) {
            return YES;
        }
    }

    // 172.16.x.x - 172.31.x.x 私有网络范围
    if ((ip & 0xFFF00000) == 0xAC100000) {
        return YES;
    }

    // 192.168.x.x 私有网络范围
    if ((ip & 0xFFFF0000) == 0xC0A80000) {
        return YES;
    }

    // 2. 检查已知VPN服务商IP段
    if ([self isKnownVPNServiceIPRange:ip]) {
        return YES;
    }

    return NO;
}

/**
 * 检查是否为已知VPN服务商IP段
 * 包含常见VPN服务提供商使用的IP段
 */
- (BOOL)isKnownVPNServiceIPRange:(uint32_t)ip {
    // Digilink VPN常用IP段: 245.x.x.x
    if ((ip & 0xFF000000) == 0xF5000000) { // 245.x.x.x
        TKLogDebug(@"检测到Digilink VPN IP段: 245.x.x.x");
        return YES;
    }

    // ExpressVPN常用IP段: 103.x.x.x, 185.x.x.x
    if ((ip & 0xFF000000) == 0x67000000 || // 103.x.x.x
        (ip & 0xFF000000) == 0xB9000000) { // 185.x.x.x
        TKLogDebug(@"检测到ExpressVPN IP段");
        return YES;
    }

    // NordVPN常用IP段: 89.x.x.x, 37.x.x.x
    if ((ip & 0xFF000000) == 0x59000000 || // 89.x.x.x
        (ip & 0xFF000000) == 0x25000000) { // 37.x.x.x
        TKLogDebug(@"检测到NordVPN IP段");
        return YES;
    }

    // Surfshark VPN常用IP段: 217.x.x.x
    if ((ip & 0xFF000000) == 0xD9000000) { // 217.x.x.x
        TKLogDebug(@"检测到Surfshark VPN IP段");
        return YES;
    }

    // 其他常见VPN IP段可以在这里继续添加

    return NO;
}

- (BOOL)detectSystemProxySettings {
    TKLogDebug(@"开始系统代理配置检测");

    CFDictionaryRef proxySettings = CFNetworkCopySystemProxySettings();
    if (proxySettings == NULL) {
        TKLogDebug(@"无法获取系统代理设置");
        return NO;
    }

    BOOL hasProxy = NO;

    // 检查代理自动配置（PAC）
    CFStringRef autoConfigURL = (CFStringRef)CFDictionaryGetValue(proxySettings, kCFNetworkProxiesProxyAutoConfigURLString);
    CFNumberRef autoConfigEnable = (CFNumberRef)CFDictionaryGetValue(proxySettings, kCFNetworkProxiesProxyAutoConfigEnable);

    if (autoConfigURL && autoConfigEnable) {
        CFIndex urlLength = CFStringGetLength(autoConfigURL);
        int enabled = 0;
        CFNumberGetValue(autoConfigEnable, kCFNumberIntType, &enabled);

        if (urlLength > 0 && enabled != 0) {
            hasProxy = YES;
            TKLogDebug(@"检测到有效的PAC代理配置");
        }
    }

    CFRelease(proxySettings);
    TKLogDebug(@"系统代理检测结果: %@", hasProxy ? @"检测到代理" : @"未检测到代理");
    return hasProxy;
}

- (BOOL)detectVPNInterface {
    TKLogDebug(@"开始明确VPN接口检测（已优化为统一分析）");

    // 使用统一网络分析结果，避免重复枚举
    TKUnifiedNetworkAnalysisResult result = [self performUnifiedNetworkAnalysis];

    TKLogDebug(@"明确VPN接口检测结果: %@", result.hasDefiniteVPN ? @"检测到VPN" : @"未检测到VPN");
    return result.hasDefiniteVPN;
}





/**
 * 分析网络接口特征进行VPN检测
 */
- (BOOL)analyzeNetworkInterfaceCharacteristics {
    TKLogDebug(@"开始网络接口特征分析（已优化为统一分析）");

    // 使用统一网络分析结果，避免重复枚举
    TKUnifiedNetworkAnalysisResult result = [self performUnifiedNetworkAnalysis];

    TKLogDebug(@"网络接口特征分析结果: %@", result.hasSystemVPN ? @"检测到系统VPN" : @"未检测到系统VPN");
    return result.hasSystemVPN;
}

/**
 * 验证用户VPN接口的真实性（已优化为统一分析）
 * 通过更严格的条件判断是否为真正的用户VPN
 */
- (BOOL)verifyUserVPNInterfaces {
    TKLogDebug(@"开始验证用户VPN接口真实性（已优化为统一分析）");

    // 使用统一网络分析结果，避免重复枚举
    TKUnifiedNetworkAnalysisResult result = [self performUnifiedNetworkAnalysis];

    TKLogDebug(@"用户VPN接口验证结果: %@", result.hasRealUserVPN ? @"确认存在用户VPN" : @"未确认用户VPN");
    return result.hasRealUserVPN;
}

// 重复的IP验证方法已合并到通用方法中

// VPN状态转换方法已移除（NEVPNManager相关）

- (BOOL)detectProxyApplications {
    TKLogDebug(@"开始代理应用检测");

    BOOL hasProxyApp = NO;
    int suspiciousPortCount = 0;

    // 检查常见代理端口（分优先级）
    // 高优先级端口：更可能是代理应用
    NSArray *highPriorityPorts = @[@8080, @8888, @9090, @3128];
    // 中优先级端口：可能被其他应用使用
    NSArray *mediumPriorityPorts = @[@1080, @8001, @8002];

    TKLogDebug(@"检查高优先级代理端口");
    for (NSNumber *port in highPriorityPorts) {
        if ([self isLocalPortInUse:[port intValue]]) {
            TKLogDebug(@"检测到高优先级代理端口: %@", port);
            hasProxyApp = YES;
            suspiciousPortCount++;
        }
    }

    // 如果高优先级端口没有检测到，再检查中优先级端口
    if (!hasProxyApp) {
        TKLogDebug(@"检查中优先级代理端口");
        for (NSNumber *port in mediumPriorityPorts) {
            if ([self isLocalPortInUse:[port intValue]]) {
                TKLogDebug(@"检测到中优先级代理端口: %@", port);
                suspiciousPortCount++;
            }
        }

        // 中优先级端口需要更多证据才能判定为代理应用
        if (suspiciousPortCount >= 2) {
            TKLogDebug(@"多个中优先级端口被占用，可能存在代理应用");
            hasProxyApp = YES;
        } else if (suspiciousPortCount == 1) {
            TKLogDebug(@"单个中优先级端口被占用，需要进一步验证");
            // 进一步检查系统代理设置
            hasProxyApp = [self checkSystemProxyPointingToLocal];
        }
    }

    // 如果端口检测没有结果，检查系统代理是否指向本地
    if (!hasProxyApp && suspiciousPortCount == 0) {
        hasProxyApp = [self checkSystemProxyPointingToLocal];
    }

    TKLogDebug(@"代理应用检测结果: %@ (可疑端口数: %d)", hasProxyApp ? @"检测到代理应用" : @"未检测到代理应用", suspiciousPortCount);
    return hasProxyApp;
}

/**
 * 检查系统代理是否指向本地地址（优化版本）
 */
- (BOOL)checkSystemProxyPointingToLocal {
    TKLogDebug(@"检查系统代理是否指向本地地址");

    CFDictionaryRef proxySettings = CFNetworkCopySystemProxySettings();
    if (proxySettings == NULL) {
        return NO;
    }

    BOOL pointsToLocal = NO;

    // 检查HTTP代理
    CFStringRef httpProxy = (CFStringRef)CFDictionaryGetValue(proxySettings, kCFNetworkProxiesHTTPProxy);
    NSString *proxyHost = (__bridge NSString *)httpProxy;
    TKLogDebug(@"代理地址: %@", proxyHost);
    if (httpProxy) {
        if ([self isLocalProxyAddress:proxyHost]) {
            TKLogDebug(@"HTTP代理指向本地地址");
            pointsToLocal = YES;
        }
    }

    CFRelease(proxySettings);
    return pointsToLocal;
}

- (nullable NSString *)getVPNInterfaceInfo {
    TKLogDebug(@"获取VPN接口信息（已优化为统一分析）");

    // 使用统一网络分析结果，避免重复枚举
    TKUnifiedNetworkAnalysisResult result = [self performUnifiedNetworkAnalysis];

    return result.definiteVPNInfo;
}

// 重复的辅助方法已合并到通用方法中

- (BOOL)isLocalPortInUse:(int)port {
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        return NO;
    }

    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = inet_addr("127.0.0.1");
    addr.sin_port = htons(port);

    int result = connect(sockfd, (struct sockaddr*)&addr, sizeof(addr));
    close(sockfd);

    return (result == 0);
}



@end
