//
//  TKTradeMainViewController.m
//  TKApp
//
//  Created by liupm on 15-3-18.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKH5MainViewController.h"
#import "TKPrivacyMainViewController.h"
#import "TKAppDelegate.h"

#import "UIWindow+TKTraitEnvironment.h"
#import "TKQYSDKManager.h"

#import "ZQJailbreakDetectTool.h"

#import <Bugly/Bugly.h>
#import "TKShareMenu.h"
#import <NetworkExtension/NetworkExtension.h>
#import <ifaddrs.h>
#import <arpa/inet.h>
#import <net/if.h>
#import <SystemConfiguration/SystemConfiguration.h>
#import <resolv.h>
#import "TKProxyDetector.h"

@interface TKH5MainViewController ()<TKPrivacyMainViewControllerDelegate, UIAlertViewDelegate, QYConversationManagerDelegate, TKShareMenuDelegate>

@property (nonatomic, readwrite, weak) TKPrivacyMainViewController *privacyMainViewController;
@property (nonatomic, readwrite, assign) BOOL isFirstInit; // 是否首次初始化

//@property (nonatomic, readwrite, strong) UIButton *testBtn;
@property (nonatomic, readwrite, strong) NSDictionary *shareDic;


@end

@implementation TKH5MainViewController{
}

#pragma mark - Init && Dealloc
//- (void)dealloc {
//
//}

#pragma mark - View life cycle
-(void)viewDidLoad{
    [super viewDidLoad];
    
    // 支持长按保存图片
    self.isSupportReadQRCodeImage = YES;
    self.isCheckScreenShotRecord = YES;   // 是否开启防截屏防录像检测
    
    // 暗黑模式适配
    //    self.view.backgroundColor  = [TKUIHelper colorWithHexString:@"#319EF2"];
    [self.view addCssClass:@".h5main"];
    [self.webView addCssClass:@".h5main"];
    
    // 根据模式设置底部安全区域颜色
    [self updateCurrentTheme];
    
    // 设置七鱼会话监听
    [[[TKQYSDKManager sharedSDK] conversationManager] setDelegate:self];
    
//    UIButton *button = [UIButton buttonWithType:UIButtonTypeContactAdd];
//    button .center = self.view.center;
//    [self.view addSubview:button];
//    [button addTarget:self action:@selector(checkIsRecord) forControlEvents:UIControlEventTouchUpInside];
    
    //    // 监听撤销隐私协议通知
    //    // 整个生命周期都会存在
    //    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(observePDFRightButtonAction:) name:NOTE_ACTION_PDF object:nil];
}


- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    // 越狱检查
    BOOL isJail = [ZQJailbreakDetectTool detectCurrentDeviceIsJailbroken];
    isJail = NO;   // 越狱测试，上线请屏蔽
    if (isJail) {
        
        // 停住启动页
        [[TKAppStartManager shareInstance] launch];
        
    } else {
        // 注意只需要弹一次
        NSString *privacyStye = (NSString *)[[TKCacheManager shareInstance] getFileCacheDataWithKey:CACHE_AGREE_PRIVACY_AGREEMENT_key];
        if (!(privacyStye && [privacyStye isEqualToString:@"1"])) {
            // 停住启动页
            [[TKAppStartManager shareInstance] launch];
        }
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
//    NSLog(@"viewDidAppear self.isFirstInit = %i", self.isFirstInit);
    
//    [self.view addSubview:self.testBtn];
    
    // 越狱检查
    BOOL isJail = [ZQJailbreakDetectTool detectCurrentDeviceIsJailbroken];
    isJail = NO;   // 越狱测试，上线请屏蔽
    if (isJail) {
        // 弹出越狱弹窗
        NSArray *windows = UIApplication.sharedApplication.windows;
        UIWindow *window = UIApplication.sharedApplication.keyWindow;
        for (long i = (windows.count - 1); i > 0; i--) {
            UIWindow *newWindow = windows[1];
            if (newWindow != UIApplication.sharedApplication.keyWindow && [newWindow isMemberOfClass:UIWindow.class]) {
                window = newWindow;
            }
        }

        [TKAlertHelper showAlert:@"检测到您的手机已越狱" title:@"温馨提示" okBtnText:@"确认" btnHandler:^(NSInteger buttonIndex) {
                    
            // 关闭程序
            exit(0);
        } parentViewController:window.rootViewController];
        return;
    }
    
    NSString *privacyStye = (NSString *)[[TKCacheManager shareInstance] getFileCacheDataWithKey:CACHE_AGREE_PRIVACY_AGREEMENT_key];
    if (privacyStye && [privacyStye isEqualToString:@"1"]) {

        if (self.isFirstInit == NO) {
            // 已同意隐私协议，初始化三方sdk
            TKAppDelegate *appdelegate = (TKAppDelegate *)[UIApplication sharedApplication].delegate;
            [appdelegate initThirdSDK];

            // 更新页面
            self.webViewUrl = @"www/m/mall/index.html";

            // 收起启动页
            [[TKAppStartManager shareInstance] hide:YES];

            self.isFirstInit = YES;
            
            // NSLog(@"viewDidAppear 更新页面");
        }

        // 通知h5检查theme，如果发生改变，刷新页面
        [self updateH5];
        
        // NSLog(@"viewDidAppear updateH5 ");
    }else{

        // 弹出隐私协议页面
        TKPrivacyMainViewController *privacyMainViewController = [[TKPrivacyMainViewController alloc] initWithName:@"privacy"];
//        privacyMainViewController.view.frame = CGRectMake(0, 100, UISCREEN_WIDTH, 300);
        privacyMainViewController.delegate = self;
        
        UIWindow *window = UIApplication.sharedApplication.windows.lastObject;
//        [window.rootViewController presentViewController:privacyMainViewController animated:YES completion:nil];
        [window.rootViewCtrl addChildViewController:privacyMainViewController];
        [window addSubview:privacyMainViewController.view];
        _privacyMainViewController = privacyMainViewController;
    }
}

#pragma mark 通知处理
-(NSArray *)listNotificationInter
{
    NSMutableArray *notes = [[super listNotificationInter] mutableCopy];
    [notes addObject:TKTraitCollectionChangeNoti];
    [notes addObject:NOTE_DOWNLOAD_PDF];
    [notes addObject:NOTE_ACTION_PDF];
    [notes addObject:NOTE_WEBVIEW_SHARE];
    [notes addObject:NOTE_NETWORK_CHANGE];//监听网络变换
    [notes addObject:UIApplicationDidBecomeActiveNotification];//监听app将要进入前台
    [notes addObject:@"privacyMenu"];
    return notes;
}

- (void)handleNotification:(NSNotification *)notification
{
    NSString *name = notification.name;
    if ([name isEqualToString:TKTraitCollectionChangeNoti])
    {
        [self traitCollectionChange:notification];
    }else if ([name isEqualToString:NOTE_DOWNLOAD_PDF])
    {
        [self handleDownloadPdfNoti:notification];
    }else if ([name isEqualToString:NOTE_ACTION_PDF])
    {
        [self observePDFRightButtonAction:notification];
    }else if ([name isEqualToString:NOTE_WEBVIEW_SHARE])
    {
        [self handleShareActionNoti:notification];
    }else if ([name isEqualToString:NOTE_NETWORK_CHANGE])
    {
        [self checkNetwork];
    }else if ([name isEqualToString:UIApplicationDidBecomeActiveNotification])
    {
        [self checkNetwork];
    }else
    {
        [super handleNotification:notification];
    }
}


#pragma mark - Selector
- (void)traitCollectionChange:(NSNotification *)noti
{
    UITraitCollection *previousTraitCollection = noti.object;
    
    // 模式发生变化
    if ([UIDevice currentDevice].systemVersion.floatValue >= 13) {
        if ([self.traitCollection hasDifferentColorAppearanceComparedToTraitCollection:previousTraitCollection]) {
            
            // 修改主题
            [self updateCurrentTheme];
            
            // 页面正在显示，刷新页面
            [self updateH5];
            // webview挂起刷新h5失效，需要等到h5显示时再刷新
        }
    }
}

- (void)updateH5
{
    // 通知h5更新模式
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    param[@"funcNo"] = @"50126";
    param[@"theme"] = [TKThemeManager shareInstance].theme;
    [self iosCallJsWithParam:param];
}

//-(void)viewDidLoad{
//    NSLog(@"----%@",[TKAesHelper stringWithAesDecryptString:[TKSystemHelper getConfigByKey:@"faceDetect.appId"] withKey:@"jjs2017"]);
//    NSLog(@"-----%@",[TKAesHelper stringWithAesDecryptString:[TKSystemHelper getConfigByKey:@"faceDetect.appKey"] withKey:@"jjs2017"]);
//    UIButton *button = [UIButton buttonWithType:UIButtonTypeContactAdd];
//    button .center = self.view.center;
//    [self.view addSubview:button];
//    [button addTarget:self action:@selector(add) forControlEvents:UIControlEventTouchUpInside];
//}
//
//-(void)add{
//    NSMutableDictionary *testDic = [NSMutableDictionary dictionary];
//    testDic[@"scheme"] = @"wx2028ad532298da16";
//    testDic[@"downloadLink"] = @"https://apps.apple.com/cn/app/id423514795";
//    testDic[@"funcNo"] = @"80050";
//    [[TKAppEngine shareInstance] callPlugin:@"80050" param:testDic module:self.name];
//}

- (void)updateCurrentTheme
{
    if ([UIDevice currentDevice].systemVersion.floatValue >= 13) {
        UIUserInterfaceStyle userInterfaceStyle = [[UITraitCollection currentTraitCollection] userInterfaceStyle];
        if (userInterfaceStyle == UIUserInterfaceStyleDark) { // 暗黑模式
            self.iphoneXBottomColor = [TKUIHelper colorWithHexString:@"#000000"];
            
            [TKThemeManager shareInstance].theme = @"theme2";
            
            // 改变网页内容背景颜色
//            [self.webView evaluateJavaScript:@"document.getElementsByTagName('body')[0].style.background='#000000'"completionHandler:nil];
        } else {
            self.iphoneXBottomColor = [TKUIHelper colorWithHexString:@"#ffffff"];
            
            [TKThemeManager shareInstance].theme = @"theme1";
            
//            [self.webView evaluateJavaScript:@"document.getElementsByTagName('body')[0].style.background='#ffffff'"completionHandler:nil];
        }
    }
}

- (void)handleDownloadPdfNoti:(NSNotification *)noti
{
    // 老版本下载逻辑
    NSDictionary *resultDic = noti.userInfo;
    if ([resultDic isKindOfClass:NSDictionary.class]) {
        //        NSString *url = resultDic[@"url"];
        NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:(NSDictionary *)[resultDic getObjectWithKey:@"param"]];
        NSString *url = [param getStringWithKey:@"downloadUrl"];
        if ([TKStringHelper isNotEmpty:url]) param[@"url"] = url;
        
        // 下载并弹出分享框
        [[TKPluginInvokeCenter shareInstance] callPlugin:@"60395" param:param moduleName:@"mall"];
    }
}


- (void)handleShareActionNoti:(NSNotification *)noti
{
    NSDictionary *userInfo = noti.userInfo;
    self.shareDic = userInfo;
    
    if ([userInfo isKindOfClass:NSDictionary.class] && userInfo.allKeys.count > 0) {
        
        // 弹出分享列表
        TKShareMenu *shareMenu = [[TKShareMenu alloc] initWithFrame:[UIScreen mainScreen].bounds];
        shareMenu.delegate = self;
        [shareMenu show];
    }
}

- (void)callBackH5ReceiveUnreadMessage:(NSInteger)allUnreadCount
{
    NSMutableDictionary *resultParam = [NSMutableDictionary dictionary];
    resultParam[@"funcNo"] = @"80316";
    resultParam[@"allUnreadCount"] = @(allUnreadCount);
    
    [self iosCallJsWithParam:resultParam];
}

- (void)checkNetwork {
    
    TKLogDebug(@"开始异步网络代理检测");

    // 使用同步代理检测工具类进行全面检测
    TKProxyDetectionResult *result = [[TKProxyDetector sharedDetector] detectAllProxyTypes];

    // 如果检测到任何类型的代理，触发回调
    if (result.hasProxy) {
        NSString *proxyTypesString = [result.typeNames componentsJoinedByString:@","];
        TKLogDebug(@"代理检测完成 - 检测到代理类型: %@", proxyTypesString);

        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        dic[@"funcNo"] = @"80322";
        dic[@"proxyTypes"] = proxyTypesString;

        // 添加详细信息
        if (result.httpProxyInfo) {
            dic[@"httpProxyInfo"] = result.httpProxyInfo;
        }
        if (result.vpnInterfaceInfo) {
            dic[@"vpnInterfaceInfo"] = result.vpnInterfaceInfo;
        }

        TKCommonService *service = [[TKCommonService alloc]init];
        [service iosCallJs:@"mall" param:dic];

        TKLogDebug(@"已触发代理检测回调，参数: %@", dic);
    } else {
        TKLogDebug(@"代理检测完成 - 未检测到任何代理");
    }
}

- (void)checkIsRecord
{
    //    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:@"80323" param:@{} moduleName:@"mall"];
    //    NSLog(@"");
    
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//
//        NSDictionary *param = @{
//            @"url" : @"https://jjdxcs.xintongfund.com/m/mall/index.html#!/privacyAgreement/index.html",
//            @"rightBtnTxt" : @"tk_plugin_50240_privacyMenu",
//            @"rightBtnMode" : @"1",
//            @"rightBtnAction" : @"privacyMenu",
//            @"rightBtnActionParam" : @{
//                @"promtpText" : @"撤销提示",
//                @"downloadUrl" : @"https://m.xintongfund.com/fund_filesystem/agreement/20210709094515.pdf",
//            },
//        };
//        [[TKPluginInvokeCenter shareInstance] callPlugin:@"50240" param:param moduleName:@"mall"];
//    });
}

//isCheckScreenShotRecord截屏录屏回调
- (void)handleCheckScreenShotRecord:(TKCheckScreenType)checkScreenType{
    NSString * isInterceptScreenshot =(NSString *)[[TKCacheManager shareInstance] getCacheDataWithKey:@"isInterceptScreenshot" cacheType:TKCacheType_Memo];

    if ([isInterceptScreenshot intValue]==1) {
        if(checkScreenType==TKCheckScreenType_Shot){
            NSString *screenCaptureTip =(NSString *)[[TKCacheManager shareInstance] getCacheDataWithKey:@"screenCaptureTip" cacheType:TKCacheType_Memo];
            if([TKStringHelper isEmpty:screenCaptureTip]){
                screenCaptureTip=@"发现正在截屏，请注意个人信息安全！";
            }
            TKLayerView *layerView=[[TKLayerView alloc] initContentView:self.rootWindow withBtnTextColor:nil];
            [layerView showTip:screenCaptureTip position:TKLayerPosition_Center];
        }else{
            NSString * screenRecordingTip =(NSString *)[[TKCacheManager shareInstance] getCacheDataWithKey:@"screenRecordingTip" cacheType:TKCacheType_Memo];
            if([TKStringHelper isEmpty:screenRecordingTip]){
                screenRecordingTip=@"发现正在录屏，请注意个人信息安全！";
            }

            TKLayerView *layerView=[[TKLayerView alloc] initContentView:self.rootWindow withBtnTextColor:nil];
            [layerView showTip:screenRecordingTip position:TKLayerPosition_Center];
        }
    }
}

- (void)reportException
{
    //    [Bugly reportExceptionWithCategory:3 name:@"自定义错误" reason:@"自定义错误" callStack:@[] extraInfo:@{@"key" : @"收到信息就对了"} terminateApp:YES];
    
    NSError *error = [NSError errorWithDomain:NSCocoaErrorDomain code:-999 userInfo:@{@"key" : @"主动上报错误"}];
    [Bugly reportError:error];

    NSException *ex = [NSException exceptionWithName:@"主动上报崩溃" reason:@"主动上报崩溃" userInfo:@{@"key" : @"收到信息就对了"}];
    [Bugly reportException:ex];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [ex raise];
    });
}

//- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
//    return UIInterfaceOrientationPortrait;
//}
//
//- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
//    return UIInterfaceOrientationMaskAllButUpsideDown;
//}
//
//- (BOOL)shouldAutorotate {
//    return YES;
//}


/// 监听pdf右上角按钮点击
- (void)observePDFRightButtonAction:(NSNotification *)noti
{
    [self showChoiceSheet:noti];
}

// 撤销协议通知
- (void)undoPrivacy:(NSNotification *)noti {
    
    NSDictionary *userInfo = noti.userInfo;
    UIViewController *object = noti.object;
    if ([userInfo isKindOfClass:NSDictionary.class] && userInfo.allKeys.count > 0) {
        //        NSString *action = [userInfo getStringWithKey:@"action"];
        NSDictionary *param = (NSDictionary *)[userInfo getObjectWithKey:@"param"];
        
        // 撤销隐私协议
        //        if ([action isEqualToString:@"undoPrivacy"]) {
        
        if ([param isKindOfClass:NSDictionary.class] && param.allKeys.count > 0 && [object isKindOfClass:UIViewController.class]) {
            
            [TKAlertHelper showConfirm:param[@"promtpText"] title:@"温馨提示" okBtnText:@"确定" cancelBtnText:@"取消" btnHandler:^(NSInteger buttonIndex) {
                // 撤销时关闭app
                if (buttonIndex == 0) {
                    // 保存结果
                    [[TKCacheManager shareInstance] saveFileCacheData:@"0" withKey:CACHE_AGREE_PRIVACY_AGREEMENT_key];
                    
                    exit(0);
                }
            } parentViewController:(UIViewController *)object];
        }
        //        }
    }
}

- (void)showChoiceSheet:(NSNotification *)noti
{
    NSDictionary *userInfo = noti.userInfo;
    UIViewController *object = noti.object;
    if ([userInfo isKindOfClass:NSDictionary.class] && userInfo.allKeys.count > 0) {
        NSString *action = [userInfo getStringWithKey:@"action"];
        NSDictionary *param = (NSDictionary *)[userInfo getObjectWithKey:@"param"];
        
        if ([action isEqualToString:@"privacyMenu"]) {
            
            __weak typeof(self)weakSelf = self;
            UIAlertController *alertVC = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
            
            if( [TKDeviceHelper getDeviceFamily] == TKUIDeviceFamilyiPad) {
                // iPad上需要设置UIAlertController这个弹出窗口的位置信息（当使用UIAlertController的UIAlertControllerStyleActionSheet时在iPad上运行会崩溃）
                UIWindow *mainApplicationWindow = [TKUIHelper mainApplicationWindow];
                alertVC.popoverPresentationController.sourceView = mainApplicationWindow;
                CGRect windowFrame = mainApplicationWindow.frame;
                alertVC.popoverPresentationController.sourceRect =  CGRectMake(0, windowFrame.size.height, windowFrame.size.width, windowFrame.size.height);
            }
            
            UIAlertAction *takePicAlbumAction = [UIAlertAction actionWithTitle:@"下载"
                                                                         style:UIAlertActionStyleDefault
                                                                       handler:^(UIAlertAction * _Nonnull action) {
                [weakSelf choiceSheet:@"1" noti:noti];
            }];
            UIAlertAction *photoAlbumAction = [UIAlertAction actionWithTitle:@"撤销"
                                                                       style:UIAlertActionStyleDefault
                                                                     handler:^(UIAlertAction * _Nonnull action) {
                [weakSelf choiceSheet:@"2"  noti:noti];
            }];
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消"
                                                                   style:UIAlertActionStyleDefault
                                                                 handler:^(UIAlertAction * _Nonnull action) {
                [weakSelf choiceSheet:@"0"  noti:noti];
            }];
            [alertVC addAction:takePicAlbumAction];
            [alertVC addAction:photoAlbumAction];
            [alertVC addAction:cancelAction];
            // 展示
            [(UIViewController *)object presentViewController:alertVC animated:YES completion:nil];
        }
    }
}

- (void)choiceSheet:action noti:(NSNotification *)noti {
    
//    [self dismissViewControllerAnimated:YES completion:nil];
    
    if ([action isEqualToString:@"1"]) {    // 下载
        
        [self handleDownloadPdfNoti:noti];
    } else if ([action isEqualToString:@"2"]) { // "撤销"
        
        [self undoPrivacy:noti];
    } else {    // 取消
        
    }
}

- (void)didSelectWechatFriendButton {
    
    NSMutableDictionary *paramMap = [NSMutableDictionary dictionaryWithDictionary:self.shareDic];
    paramMap[@"shareType"] = @"22"; // 50231使用
    
    [[TKPluginInvokeCenter shareInstance] callPlugin:@"50231" param:paramMap moduleName:@"mall"];
//    NSLog(@"");
    
    
}

- (void)didSelectWechatTimelineButton {
    NSMutableDictionary *paramMap = [NSMutableDictionary dictionaryWithDictionary:self.shareDic];
    paramMap[@"shareType"] = @"23"; // 50231使用
    
    [[TKPluginInvokeCenter shareInstance] callPlugin:@"50231" param:paramMap moduleName:@"mall"];
}

- (void)h5LoadFinish {
    [self checkNetwork];
}

#pragma mark - TKPrivacyMainViewControllerDelegate
-(void)viewController:(TKPrivacyMainViewController *)ctl agreePrivacyAgreementSuccess:(BOOL)success{
    
    // 新的监管需求不能在不同意隐私协议的情况下关闭app
    if (success) {
//        [ctl dismissViewControllerAnimated:YES completion:nil];
        [_privacyMainViewController removeFromParentViewController];
        [_privacyMainViewController.view removeFromSuperview];
        
        // 保存结果
        [[TKCacheManager shareInstance] saveFileCacheData:@"1" withKey:CACHE_AGREE_PRIVACY_AGREEMENT_key];
        
        // 初始化第三方sdk
        TKAppDelegate *appdelegate = (TKAppDelegate *)[UIApplication sharedApplication].delegate;
        [appdelegate initThirdSDK];
        
        // 更新页面
        self.webViewUrl = @"www/m/mall/index.html";
        
        // 收起启动页
        [[TKAppStartManager shareInstance] hide:YES];
        
        // 标记初始化，防止弹出像相册等控制器的时候，又再初始化一次
        self.isFirstInit = YES;
        
        // 通知h5检查theme，如果发生改变，刷新页面
        [self updateH5];
    }
    else
    {
        // 直接退出
        exit(0);
    }
}



//#pragma mark - UIAlertViewDelegate
//- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex {
//
//    // 撤销时关闭app
//    if (buttonIndex == 0) {
//        // 保存结果
//        [[TKCacheManager shareInstance] saveFileCacheData:@"0" withKey:CACHE_AGREE_PRIVACY_AGREEMENT_key];
//
//        exit(0);
//    }
//}

#pragma mark - QYConversationManagerDelegate
/**
 *  会话未读数变化
 *
 *  @param count 未读数
 */
- (void)onUnreadCountChanged:(NSInteger)count {
    
    TKLogInfo(@"思迪七鱼客服日志：会话未读数变化发生改变=%i", (int)count);
    
    if (count > 0) {
        [self callBackH5ReceiveUnreadMessage:count];
    }
}

//- (void)onSessionListChanged:(NSArray<QYSessionInfo *> *)sessionList {
//    NSString *lastMessageText = nil;
//    if (sessionList.count > 0) {
//       lastMessageText = sessionList.firstObject.lastMessageText;
//    }
//
//    TKLogInfo(@"思迪七鱼客服日志：会话列表发生改变=%@, 第一个数组的lastMessageText = %@", sessionList, lastMessageText);
//}

///**
// *  接收消息
// */
//- (void)onReceiveMessage:(QYMessageInfo *)message {
//    TKLogInfo(@"思迪七鱼客服日志：接收到消息=%@", message);
//}


//- (UIButton *)testBtn {
//    if (!_testBtn) {
//        _testBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//        _testBtn.frame = CGRectMake(50, 50, 200, 50);
//        _testBtn.backgroundColor = UIColor.redColor;
//        [_testBtn setTitle:@"点我就崩溃给你看" forState:UIControlStateNormal];
//        [_testBtn addTarget:self action:@selector(reportException) forControlEvents:UIControlEventTouchUpInside];
//    }
//
//    return _testBtn;
//}

@end
